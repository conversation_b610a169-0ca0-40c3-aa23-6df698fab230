import PropTypes from 'prop-types';
import React, { useMemo, useState } from 'react';
import Skeleton from 'react-loading-skeleton';
import styled from 'styled-components';
import Button from '../Button/Button';
import Checkbox from '../Checkbox';
import DataHeader from '../DataHeader';
import Row from '../Row';
import Pagination from './Pagination';
import SortButtons from './SortButtons';

export const DataTableContainer = styled.div`
  overflow-x: auto;
`;

export const StyledDataTable = styled.table`
  width: calc(100% - 1px);
  border-spacing: 0;
  border-collapse: collapse;
`;

export const HeaderTableRow = styled.tr`
  background-color: #bfe8f9;
  color: #333333;
  border-radius: 2px 2px 0 0;
  text-align: left;
`;

export const TableRow = styled.tr`
  color: #333333;
  background-color: ${props =>
    props.selected ? '#DDEAEF' : props.striped ? '#f3f3f3' : 'white'};

  border: 1px solid rgba(220, 220, 220, 0.56);
  border-bottom: 0;
  transition: background-color 0.25s ease;
  &:last-child {
    border-bottom: 1px solid rgba(220, 220, 220, 0.56);
  }

  &:hover {
    background-color: #edfae0;
  }
`;

export const TableData = styled.td`
  height: 40px;
  padding: 5px 16px;
  font-size: ${props => props.theme.fontSize.s};
  font-weight: 300;
  min-width: ${props => props.minWidth}px;
  text-align: ${props => props.textAlign || 'left'};
`;

export const NoDataContainer = styled(TableData)`
  text-align: center;
`;

export const TableHeader = styled.th`
  height: 40px;
  padding: 5px 16px;
  font-size: ${props => props.theme.fontSize.s};
  font-weight: normal;
  min-width: ${props => props.minWidth}px;
`;

export const TableFooterCell = styled(TableHeader)`
  font-weight: bold;
  text-align: ${props => props.textAlign || 'left'};
`;

export const TableHeaderLabel = styled.span``;

export const TableDataLink = styled(Button)`
  text-align: left;
  background-color: transparent;
  color: #009cde;

  &:hover {
    text-decoration: underline;
  }
`;

export const SelectRowCheckbox = styled(Checkbox)`
  background-color: white;
`;

const DataTable = ({
  data = [],
  config,
  pagination,
  headerOptions,
  footer,
  loading = false,
  minCellWidth = 140,
  className,
  series = false,
  selected = false,
  setSelected,
  withoutPagination = true,
}) => {
  let tableData = [...data];

  if (loading && pagination && data.length < +pagination.limit) {
    for (let i = data.length; i < +pagination.limit; ++i) {
      tableData.push(undefined);
    }
  }

  const [sorting, setSorting] = useState(
    Object.keys(config).reduce((acc, curr) => {
      acc[curr] = null;
      return acc;
    }, {})
  );

  const rowIds = useMemo(() => {
    const rowIds = {};
    for (const data of tableData) {
      if (data) {
        rowIds[data.id] = data;
      }
    }
    return rowIds;
  }, [JSON.stringify(tableData)]);

  const isAllSelected = useMemo(() => {
    for (const key of Object.keys(rowIds)) {
      if (!selected[key]) return false;
    }

    return true;
  }, [JSON.stringify(rowIds), JSON.stringify(selected)]);

  return (
    <React.Fragment>
      {headerOptions && <DataHeader>{headerOptions}</DataHeader>}
      <DataTableContainer className={className}>
        <StyledDataTable>
          <thead>
            <HeaderTableRow>
              {!!selected && (
                <TableHeader>
                  <SelectRowCheckbox
                    name="select-all"
                    disabled={loading}
                    checked={loading ? false : isAllSelected}
                    onChange={() => {
                      if (isAllSelected) {
                        setSelected(tableData);
                      } else {
                        const keys = Object.keys(selected);
                        setSelected(
                          tableData.filter(data => !keys.includes(data.id))
                        );
                      }
                    }}
                  />
                </TableHeader>
              )}
              {Object.keys(config).map((key, cellIndex) => {
                if (!config[key]) return null;
                const { sortable, renderLabelAs, hideRow } = config[key];
                if (!hideRow) {
                  return (
                    <TableHeader key={cellIndex} minWidth={minCellWidth}>
                      <Row style={{ justifyContent: 'space-between' }}>
                        <span>
                          {renderLabelAs
                            ? renderLabelAs()
                            : config[key].headerLabel}
                        </span>
                        {sortable && (
                          <SortButtons
                            sortBy={sorting[key]}
                            onClick={() => {
                              if (sorting[key] === null) {
                                setSorting({ ...sorting, [key]: 'ASC' });
                              } else if (sorting[key] === 'ASC') {
                                setSorting({ ...sorting, [key]: 'DESC' });
                              } else if (sorting[key] === 'DESC') {
                                setSorting({ ...sorting, [key]: null });
                              }
                            }}
                          />
                        )}
                      </Row>
                    </TableHeader>
                  );
                } else {
                  return null;
                }
              })}
            </HeaderTableRow>
          </thead>
          <tbody>
            {(!tableData || tableData.length === 0) && (
              <TableRow>
                <NoDataContainer colSpan={Object.keys(config).length}>
                  No data
                </NoDataContainer>
              </TableRow>
            )}
            {[...tableData]
              .sort((a, b) => {
                for (const key of Object.keys(config)) {
                  if (sorting[key] === null) continue;

                  const { sortable } = config[key];

                  let aValue =
                    typeof sortable === 'function' ? sortable(a, key) : a[key];
                  let bValue =
                    typeof sortable === 'function' ? sortable(b, key) : b[key];

                  if (aValue !== bValue) {
                    if (
                      typeof aValue === 'string' &&
                      typeof bValue === 'string'
                    ) {
                      aValue = aValue.toLowerCase();
                      bValue = bValue.toLowerCase();
                    }
                    if (sorting[key] === 'ASC') {
                      return aValue > bValue ? 1 : -1;
                    } else {
                      return aValue < bValue ? 1 : -1;
                    }
                  } else {
                    continue;
                  }
                }
                return 0;
              })
              .map((row, rowIndex) => (
                <TableRow
                  selected={!!selected && row && !!selected[row.id]}
                  striped={rowIndex & (2 !== 0)}
                  key={rowIndex}
                >
                  {!!selected &&
                    (row ? (
                      <TableData>
                        <SelectRowCheckbox
                          name={`select-${row.id}`}
                          checked={!!selected[row.id]}
                          onChange={() => setSelected([row])}
                        />
                      </TableData>
                    ) : (
                      <TableData>
                        <Skeleton />
                      </TableData>
                    ))}

                  {Object.keys(config).map((key, cellIndex) => {
                    if (!config[key]) return null;

                    if (loading && tableData[rowIndex] === undefined)
                      return (
                        <TableData key={cellIndex} minWidth={minCellWidth}>
                          <Skeleton />
                        </TableData>
                      );

                    const { renderAs, onClick, textAlign, hideRow } =
                      config[key];

                    if (!hideRow) {
                      if (onClick) {
                        return (
                          <TableData
                            key={cellIndex}
                            minWidth={minCellWidth}
                            textAlign={textAlign}
                          >
                            <TableDataLink onClick={() => onClick(row)}>
                              {renderAs ? renderAs(row, key) : row[key]}
                            </TableDataLink>
                          </TableData>
                        );
                      }

                      return (
                        <TableData
                          key={cellIndex}
                          minWidth={minCellWidth}
                          textAlign={textAlign}
                        >
                          {renderAs ? renderAs(row, key) : row[key]}
                        </TableData>
                      );
                    } else {
                      return null;
                    }
                  })}
                </TableRow>
              ))}
          </tbody>
          {footer && (
            <tfoot>
              <HeaderTableRow>
                {Object.keys(config).map((key, index) => {
                  const { hideRow } = config[key];
                  if (!hideRow) {
                    return (
                      <TableFooterCell
                        textAlign={config[Object.keys(config)[index]].textAlign}
                        key={index}
                      >
                        {footer[key]}
                      </TableFooterCell>
                    );
                  } else {
                    return null;
                  }
                })}
              </HeaderTableRow>
            </tfoot>
          )}
        </StyledDataTable>
      </DataTableContainer>
      {!loading && pagination && withoutPagination && (
        <Pagination pagination={pagination} series={series} />
      )}
    </React.Fragment>
  );
};

DataTable.propTypes = {
  /** Bool for loading state */
  loading: PropTypes.bool,

  /** Displayed title for the table */
  title: PropTypes.string,

  /** Array of objects with arbtirary keys
   *  used to render the rows of the table */
  data: PropTypes.arrayOf(PropTypes.object),

  /** Accepts an object with keys same of the objects of `data`.
   *  Only keys present in `config` will be rendered in the table.
   *  Keys will be mapped to objects with shape of
   *  {
   *    headerLabel: Will render `th` in corresponding column,
   *    sortable: Will render sort icons in `th`,
   *    renderAs: (row, key) => JSX, for custom rendering,
   *              Also used for keys not in data (like actions),
   *    onClick: data => {}, fn when clicking on col
   *  }
   */
  config: PropTypes.object.isRequired,

  pagination: PropTypes.shape({
    start: PropTypes.any.isRequired,
    startKeys: PropTypes.any,
    withStartKeys: PropTypes.bool,
    limit: PropTypes.number.isRequired,
    count: PropTypes.number.isRequired,
    handleChange: PropTypes.func.isRequired,
  }),

  /** Will render other options to left of the `title` such as create button, etc */
  headerOptions: PropTypes.element,

  minCellWidth: PropTypes.number,
  footer: PropTypes.object,
  className: PropTypes.string,

  series: PropTypes.any,

  selected: PropTypes.object,
  setSelected: PropTypes.func,
  withoutPagination: PropTypes.bool,
};

export default DataTable;
