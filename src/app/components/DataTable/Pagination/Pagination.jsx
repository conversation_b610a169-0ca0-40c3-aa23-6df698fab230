import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import PropTypes from 'prop-types';
import React from 'react';
import styled from 'styled-components';
import Dropdown from '../../Dropdown';

export const PaginationContainer = styled.div`
  margin-top: 20px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
`;

export const PaginationStepContainer = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  color: #4a90e2;
  font-size: ${props => props.theme.fontSize.s};

  @media (max-width: ${props => props.theme.breakpoint.phone}) {
    min-width: 100%;
  }

  & > * {
    margin-right: 10px;
  }
`;

export const PaginationStepButton = styled.button`
  border: 0.5px solid #bbbbbb;
  border-radius: 2px;
  height: 30px;
  background-color: #f9f9f9;
  box-shadow: 0 1px 1px 0 rgba(221, 221, 221, 0.5);
  padding: 0 10px;
  color: #4a90e2;
  font-size: ${props => props.theme.fontSize.s};

  transition: background-color 0.15s ease;
  cursor: pointer;
  &:active:not(:disabled) {
    background-color: #d6f3ff;
  }

  &:disabled {
    color: #8b8b8b;
    cursor: initial;
  }
`;

export const PaginationStepIcon = styled(FontAwesomeIcon)`
  position: relative;
  left: 2px;
`;

const PaginationSizeContainer = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  color: #4a4a4a;
  font-size: ${props => props.theme.fontSize.s};

  @media (max-width: ${props => props.theme.breakpoint.phone}) {
    margin-top: 10px;
    min-width: 100%;
  }
`;

const PaginationSizeLabel = styled.span`
  margin-left: 10px;
`;

const Pagination = ({ pagination, series }) => {
  const {
    startKey,
    withStartKeys,
    cursors,
    limit,
    paginationLabel,
    count,
    handleChange,
  } = pagination;

  const totalPages = count === 0 ? 1 : Math.ceil(count / limit);

  let page = 0;

  if (!series) {
    for (let i = 0; i < cursors.length; ++i) {
      let cursor =
        typeof cursors[i] === 'object' ? { ...cursors[i] } : cursors[i];

      if (JSON.stringify(cursor) === JSON.stringify(startKey)) {
        page = i + 1;
        break;
      }
    }
  } else {
    page = series.page;
  }

  const isFirst = page === 1;
  const isLast = series ? series.isLastPage : page === totalPages;

  const startingIndex = limit * page - limit + 1;
  const endingIndex = Math.min(limit * page, count);

  return (
    <PaginationContainer>
      <PaginationStepContainer>
        <PaginationStepButton
          disabled={isFirst}
          onClick={() => {
            if (series) {
              series.setPage(page - 1);
            } else {
              handleChange({
                startKey: cursors[page - 2],
                limit: pagination.limit,
              });
            }
          }}
        >
          <PaginationStepIcon size="lg" icon="caret-down" rotation={90} />
        </PaginationStepButton>
        {!series && (
          <>
            <span>{page}</span>
            <>
              <span> of </span>
              <span>{totalPages}</span>
            </>
          </>
        )}
        <PaginationStepButton
          disabled={isLast}
          onClick={() => {
            if (series) {
              series.setPage(page + 1);
            } else {
              handleChange({ startKey: cursors[page], limit: pagination.limit });
            }
          }}
        >
          <PaginationStepIcon size="lg" icon="caret-up" rotation={90} />
        </PaginationStepButton>
        {series && paginationLabel && (
          <PaginationSizeLabel>
            {count === 0 ? `Showing no records` : `Showing ${count} Records`}
          </PaginationSizeLabel>
        )}
      </PaginationStepContainer>
      <PaginationSizeContainer>
        <Dropdown
          value={limit}
          options={[
            { label: '10', value: 10 },
            { label: '20', value: 20 },
            { label: '50', value: 50 },
            { label: '100', value: 100 },
          ]}
          onChange={limit => {
            let changes = { startKey: pagination.startKey, limit };

            if (withStartKeys) {
              changes = { startKeys: pagination.startKey, limit };
            }

            if (!series) {
              changes.startKey = cursors[0];
            }

            handleChange(changes);
          }}
        />
        {!series && (
          <PaginationSizeLabel>
            {count === 0
              ? `Showing no records`
              : `Showing ${startingIndex} to ${endingIndex} of ${count} records`}
          </PaginationSizeLabel>
        )}
      </PaginationSizeContainer>
    </PaginationContainer>
  );
};

Pagination.propTypes = {
  pagination: PropTypes.shape({
    count: PropTypes.number.isRequired,
    startKey: PropTypes.any.isRequired,
    startKeys: PropTypes.any,
    cursors: PropTypes.any,
    handleChange: PropTypes.any,
    withStartKeys: PropTypes.bool,
    paginationLabel: PropTypes.bool.isRequired,
    limit: PropTypes.number.isRequired,
  }).isRequired,
  series: PropTypes.any,
};

export default Pagination;
