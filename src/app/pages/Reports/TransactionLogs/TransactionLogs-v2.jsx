import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import React, { useContext, useState, useEffect } from 'react';
import { useQuery, useMutation } from '@apollo/client';

// Components
import { ExportButton } from '../../../components/Button/ExportButton';
import FileButton from '../../../components/Button/FileButton';
import DataContainer from '../../../components/DataContainer';
import DataTable from '../../../components/DataTable';
import { FIELD_TYPES } from '../../../components/Form/constants';
import GlobalSearch from '../../../components/GlobalSearch';
import Header from '../../../components/Header';
import { AlertModal } from '../../../components/Modal';
import Page from '../../../components/Page';

// Hooks & Context
import AuthContext from '../../../context/AuthContext/AuthContext';
import NotificationContext from '../../../context/NotificationContext';
// IMPORTANT: Make sure you are importing the new, refactored hook
import useQueryReportSeries from '../../../hooks/useQueryReportSeries-v3';
// Assuming this hook is also refactored to the new pattern
import useQueryReportSeriesWithFile from '../../../hooks/useQueryReportSeriesWithFile';

// Utils & Styled Components
import formatCurrency from '../../../utils/formatCurrency';
import formatUtcDate from '../../../utils/formatUtcDate';
import {
  ColumnVisibilityDropdown,
  ResponsiveRow,
  TransactionLogHighlight,
} from '../styled';
import { json2CSVYTD } from '../../../components/GlobalSearch/utils';
import styled from 'styled-components';

// Queries & Mutations
import { EXPORT_REPORTS } from '../mutation';
import {
  GET_CHANNEL_OPTIONS,
  GET_TRANSACTION_LOGS_INFO,
  BULKUPLOAD,
  REPORT_PATH,
} from './query';

const BulkSearchButton = styled.div``;

const PAYMENT_GATEWAYS = [
  { label: 'GCash', value: 'gcash' },
  { label: 'Xendit', value: 'xendit' },
];

// Utility function to safely download files
const downloadFile = (content, filename, mimeType = 'text/csv') => {
  // Check if we're in a browser environment
  if (typeof window === 'undefined' || !document) {
    console.warn('File download is only available in browser environment');
    return;
  }

  try {
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url); // Clean up the URL object
  } catch (error) {
    console.error('Error downloading file:', error);
  }
};

const TransactionLogs = () => {
  const { reportPermissions } = useContext(AuthContext);
  const { addNotif } = useContext(NotificationContext);

  // --- State Management ---
  const [isBulkUpload, setIsBulkUpload] = useState(false);

  // --- Data Fetching (Regular Search) ---
  const {
    pagination,
    setNewPagination,
    filter,
    setFilter,
    data,
    loading,
    page,
    setPage,
    isLastPage,
  } = useQueryReportSeries(GET_TRANSACTION_LOGS_INFO, REPORT_PATH, {
    pagination: {
      startKey: '',
      limit: 10,
    },
  });

  // --- Data Fetching (Bulk Search) ---
  const {
    pagination: bulkPagination,
    setNewPagination: setNewBulkPagination,
    setFile,
    file,
    data: transactionData,
    loading: isLoadingTransactionData,
    page: bulkPage,
    setPage: setBulkPage,
    isLastPage: isBulkLastPage,
    loadData,
    error: bulkError,
  } = useQueryReportSeriesWithFile(BULKUPLOAD, 'uploadTransactions', {
    pagination: {
      startKey: '',
      limit: 10,
    },
  });

  const { data: channelData, loading: isLoadingChannels } =
    useQuery(GET_CHANNEL_OPTIONS);

  const { data: transactionDataDownload } = useQuery(BULKUPLOAD, {
    variables: { file, pagination: { limit: 1000 } },
    fetchPolicy: 'network-only',
    skip: !file,
  });

  const [logExtraction] = useMutation(EXPORT_REPORTS);

  const [visibleColumns, setVisibleColumns] = useState([
    'paymentId',
    'createDateTime',
    'channelId',
    'customerId',
    'customerName',
    'gatewayProcessor',
    'paymentMethod',
    'sessionId',
    'status',
    'totalAmount',
  ]);

  const [modalState, setModalState] = useState({
    confirmDownload: false,
    successDownload: false,
    fileInvalid: false,
    uploadErrorMessage: '',
  });

  useEffect(() => {
    if (isLoadingTransactionData) {
      loadData();
    }
  }, []);

  useEffect(() => {
    if (
      isLoadingTransactionData &&
      bulkError &&
      bulkError.networkError?.result &&
      bulkError.networkError.result.message === 'column_mismatched'
    ) {
      setModalState(prev => ({
        ...prev,
        uploadErrorMessage: 'Column Mismatched',
        fileInvalid: true,
      }));
    }
  }, [bulkError]);

  const channelOptions =
    !isLoadingChannels && channelData
      ? [
          { value: null, label: 'Any' },
          ...channelData.channelsLoose.map(channel => ({
            value: channel.id,
            label: channel.name,
          })),
        ]
      : [{ value: null, label: 'Any' }];

  const globalSearchFields = [
    {
      label: 'Payment ID',
      name: 'paymentId',
      type: FIELD_TYPES.TEXT,
      isKey: true,
    },
    { label: 'Payment Code', name: 'paymentCode', type: FIELD_TYPES.TEXT },
    {
      label: 'Account No.',
      name: 'accountId',
      type: FIELD_TYPES.TEXT,
      isKey: true,
    },
    {
      label: 'Channel Name',
      name: 'channelId',
      type: FIELD_TYPES.SELECT,
      options: channelOptions,
      isKey: true,
    },
    { label: 'Email Address', name: 'email', type: FIELD_TYPES.TEXT },
    {
      label: 'Payment Method',
      name: 'paymentMethod',
      type: FIELD_TYPES.SELECT,
      options: [
        { value: null, label: 'Any' },
        { value: 'gcash', label: 'GCash' },
        { value: 'otc', label: 'Xendit OTC' },
        { value: 'card_straight', label: 'Xendit CC/DC Straight' },
        { value: 'card_installment', label: 'Xendit CC/DC Installment' },
      ],
      disabled: ({
        prodDesc,
        status,
        amountCurrency,
        amountValue,
        mobileNumber,
      }) =>
        !!(prodDesc || status || amountCurrency || amountValue || mobileNumber),
    },
    {
      label: 'Status',
      name: 'status',
      type: FIELD_TYPES.SELECT,
      disabled: ({
        prodDesc,
        paymentMethod,
        amountCurrency,
        amountValue,
        mobileNumber,
      }) =>
        !!(
          prodDesc ||
          paymentMethod ||
          amountCurrency ||
          amountValue ||
          mobileNumber
        ),
      options: [
        { value: null, label: 'Any' },
        { value: 'ECPAY_GENERATED', label: 'ECPAY_GENERATED' },
        { value: 'ECPAY_SESSION_CREATED', label: 'ECPAY_SESSION_CREATED' },
        { value: 'POSTED', label: 'PAYMENT_POSTED' },
        { value: 'ECPAY_AUTHORISED', label: 'ECPAY_AUTHORISED' },
        { value: 'ECPAY_EXPIRED', label: 'ECPAY_EXPIRED' },
        { value: 'WEB_SESSION_CREATED', label: 'WEB_SESSION_CREATED' },
        {
          value: 'XENDIT_CALLBACK_RECEIVED',
          label: 'XENDIT_CALLBACK_RECEIVED',
        },
      ],
    },
    {
      label: 'Total Amount',
      name: 'totalAmount',
      type: FIELD_TYPES.NUMBER,
      noOperator: true,
      disabled: ({
        prodDesc,
        status,
        amountCurrency,
        paymentMethod,
        mobileNumber,
        amountValue,
      }) =>
        !!(
          prodDesc ||
          paymentMethod ||
          amountCurrency ||
          status ||
          mobileNumber ||
          amountValue
        ),
    },
    {
      label: 'Settlement Amount',
      name: 'amount',
      type: FIELD_TYPES.NUMBER,
      noOperator: true,
      disabled: ({
        prodDesc,
        status,
        amountCurrency,
        paymentMethod,
        mobileNumber,
        totalAmount,
      }) =>
        !!(
          prodDesc ||
          paymentMethod ||
          amountCurrency ||
          status ||
          mobileNumber ||
          totalAmount
        ),
    },
    {
      label: 'Mobile Number',
      name: 'mobile',
      type: FIELD_TYPES.NUMBER,
      noOperator: true,
      disabled: ({
        prodDesc,
        status,
        amountCurrency,
        amountValue,
        paymentMethod,
      }) =>
        !!(
          prodDesc ||
          status ||
          amountCurrency ||
          amountValue ||
          paymentMethod
        ),
    },
    { label: 'Date', name: 'createDateTime', type: FIELD_TYPES.DATE_RANGE },
    {
      label: 'Payment Gateway',
      name: 'gatewayProcessor',
      type: FIELD_TYPES.SELECT,
      options: PAYMENT_GATEWAYS,
    },
    {
      label: 'Bill Type',
      name: 'transactionType',
      type: FIELD_TYPES.SELECT,
      options: [
        { value: null, label: 'Any' },
        { value: 'G', label: 'Bill Type' },
        { value: 'N', label: 'Non-Bill Type' },
      ],
    },
  ];

  const tableConfig = {
    paymentId: { headerLabel: 'Payment ID', sortable: true },
    createDateTime: {
      headerLabel: 'Created Date',
      sortable: true,
      renderAs: data => formatUtcDate(data.createDateTime?.replace('Z', '')),
    },
    channelId: {
      headerLabel: 'Channel',
      sortable: true,
      renderAs: data =>
        !isLoadingChannels && channelData
          ? channelData.channelsLoose.find(
              channel => channel.id === data.channelId
            )?.name || data.channelId
          : data.channelId,
    },
    customerId: { headerLabel: 'Customer ID', sortable: true },
    customerName: { headerLabel: 'Customer Name', sortable: true },
    gatewayProcessor: { headerLabel: 'Gateway Processor', sortable: true },
    paymentMethod: { headerLabel: 'Payment Method', sortable: true },
    sessionId: { headerLabel: 'Session ID', sortable: true },
    status: { headerLabel: 'Status', sortable: true },
    totalAmount: {
      headerLabel: 'Total Amount',
      sortable: true,
      renderAs: data => formatCurrency(data.totalAmount, true),
    },
    oona: {
      headerLabel: 'Oona',
      sortable: true,
      renderAs: data => formatCurrency(data.oona, true),
    },
    budgetProtect: {
      headerLabel: 'Budget Protect',
      sortable: true,
      renderAs: data => formatCurrency(data.budgetProtect, true),
    },
    convenienceFeeAmount: {
      headerLabel: 'Convenience Fee Amount',
      sortable: true,
      renderAs: data => formatCurrency(data.convenienceFeeAmount, true),
    },
    accountId: {
      headerLabel: 'Account ID',
      sortable: true,
      renderAs: data => data.settlement?.accountId || '-',
    },
    accountType: {
      headerLabel: 'Account Type',
      sortable: true,
      renderAs: data => data.settlement?.accountType || '-',
    },
    amount: {
      headerLabel: 'Settlement Amount',
      sortable: true,
      renderAs: data =>
        data.settlement?.amount
          ? formatCurrency(data.settlement?.amount, true)
          : '-',
    },
    brand: {
      headerLabel: 'Brand',
      sortable: true,
      renderAs: data => data.settlement?.brand || '-',
    },
    email: {
      headerLabel: 'Email',
      sortable: true,
      renderAs: data => data.settlement?.email || '-',
    },
    mobile: {
      headerLabel: 'Mobile Number',
      sortable: true,
      renderAs: data => data.settlement?.mobile || '-',
    },
    transactionType: {
      headerLabel: 'Settlement Txn Type',
      sortable: true,
      renderAs: data => {
        if (data.settlement?.transactionType === 'G') return 'Bill';
        if (data.settlement?.transactionType === 'N') return 'Non-Bill';
        return data.settlement?.transactionType || '-';
      },
    },
    settlementStatus: {
      headerLabel: 'Settlement Status',
      sortable: true,
      renderAs: data => data.settlement?.status || '-',
    },
    refundId: { headerLabel: 'Refund ID', sortable: true },
    refundApprovalStatus: { headerLabel: 'Refund Status', sortable: true },
    refundReason: { headerLabel: 'Refund Reason', sortable: true },
    refundAmount: {
      headerLabel: 'Refund Amount',
      sortable: true,
      renderAs: data => formatCurrency(data.refundAmount, true),
    },
  };

  const handleBulkReset = () => {
    setIsBulkUpload(false);
    setFile(null);
    setBulkPage(1);
  };

  const handleBulkUpload = uploadedFile => {
    const extension = uploadedFile.name.split('.').pop();
    const reader = new FileReader();

    reader.onload = function () {
      const lines = this.result.split('\r').length;
      if (uploadedFile.size > 5000000) {
        setModalState(prev => ({
          ...prev,
          fileInvalid: true,
          uploadErrorMessage: 'File must not be more than 5mb',
        }));
      } else if (extension !== 'csv') {
        setModalState(prev => ({
          ...prev,
          fileInvalid: true,
          uploadErrorMessage: 'File must be a CSV',
        }));
      } else if (lines > 1001) {
        setModalState(prev => ({
          ...prev,
          fileInvalid: true,
          uploadErrorMessage: 'CSV Row Data Exceed to 1000 rows',
        }));
      } else {
        setFile(uploadedFile);
        setIsBulkUpload(true);
        loadData();
      }
    };

    reader.readAsText(uploadedFile);
  };

  const handleDownloadConfirm = async () => {
    setModalState(s => ({ ...s, confirmDownload: false }));
    if (!isBulkUpload) {
      const notifTime = new Date().getTime();
      addNotif({
        id: `TRANS-${notifTime}`,
        notifTime,
        type: 'info',
        title: 'Downloading Report File',
        message: 'Downloading Transaction Logs Report',
        isProgress: true,
        progressData: { progress: 0, isProgressive: false },
        isLocal: true,
        reportDLParams: {
          query: GET_TRANSACTION_LOGS_INFO,
          path: REPORT_PATH,
          variables: { filter, pagination: { limit: 1000 } },
          onDownload: () =>
            logExtraction({ variables: { data: { type: 'transaction' } } }),
          tableConfig,
          fileName: 'transaction-logs.csv',
        },
      });
    } else {
      const csvData = await json2CSVYTD(
        transactionDataDownload?.uploadTransactions?.filteredData || [],
        tableConfig
      );
      downloadFile(csvData, 'transaction-logs.csv');
    }
  };

  // Remove these variables as we'll handle the logic directly in the JSX

  return (
    <>
      <Page>
        <Header
          withHome
          title="Transaction Logs"
          path={['Reports', 'Transaction Logs']}
        />
        <DataContainer>
          <DataTable
            minCellWidth={200}
            headerOptions={
              <>
                <GlobalSearch
                  onSearch={filter => {
                    const newPagination = {
                      ...pagination,
                      startKey: '',
                    };
                    setFilter(filter);
                    setNewPagination(newPagination);
                  }}
                  fields={globalSearchFields}
                />
                <ResponsiveRow>
                  {isBulkUpload ? (
                    <BulkSearchButton>
                      <ExportButton
                        icon="times"
                        iconPosition="left"
                        disabled={loading}
                        onClick={handleBulkReset}
                      >
                        Reset Bulk Search
                      </ExportButton>
                    </BulkSearchButton>
                  ) : (
                    <BulkSearchButton>
                      {reportPermissions.Transaction.import && (
                        <FileButton
                          disabled={loading}
                          loading={loading}
                          onImport={handleBulkUpload}
                          onTemplate={() => {
                            const templateContent =
                              'reference,accountNumber,date from,date to\nTEST1591718837100693,*********,04/23/2021,04/23/2021\n';
                            downloadFile(
                              templateContent,
                              'bulk-search-transaction-logs.csv'
                            );
                          }}
                        >
                          Bulk Search{' '}
                          <FontAwesomeIcon
                            style={{ marginLeft: 10 }}
                            icon="angle-down"
                          />
                        </FileButton>
                      )}
                    </BulkSearchButton>
                  )}
                  {reportPermissions.Transaction.export && (
                    <ExportButton
                      icon="file-csv"
                      iconPosition="left"
                      disabled={
                        isBulkUpload ? isLoadingTransactionData : loading
                      }
                      onClick={() =>
                        setModalState(s => ({ ...s, confirmDownload: true }))
                      }
                    >
                      CSV
                    </ExportButton>
                  )}
                  <ColumnVisibilityDropdown
                    multi
                    placeholder="Visible Columns"
                    value={visibleColumns}
                    options={Object.keys(tableConfig).map(key => ({
                      value: key,
                      label: tableConfig[key].headerLabel,
                    }))}
                    onChange={setVisibleColumns}
                  />
                </ResponsiveRow>
              </>
            }
            loading={isBulkUpload ? isLoadingTransactionData : loading}
            data={isBulkUpload ? transactionData : data}
            config={Object.keys(tableConfig).reduce((config, key) => {
              if (visibleColumns.includes(key)) config[key] = tableConfig[key];
              return config;
            }, {})}
            pagination={
              isBulkUpload
                ? {
                    ...bulkPagination,
                    start: bulkPagination.startKey,
                    withStartKeys: true,
                    count: 0,
                    cursors: [],
                    handleChange: setNewBulkPagination,
                  }
                : {
                    ...pagination,
                    start: pagination.startKey,
                    withStartKeys: true,
                    count: 0,
                    cursors: [],
                    handleChange: setNewPagination,
                  }
            }
            series={
              isBulkUpload
                ? {
                    page: bulkPage,
                    setPage: setBulkPage,
                    isLastPage: isBulkLastPage,
                  }
                : { page, setPage, isLastPage }
            }
          />
        </DataContainer>
      </Page>
      <AlertModal
        isOpen={modalState.confirmDownload}
        title="Export Reports Alert"
        header="ARE YOU SURE?"
        variant="warn"
        icon="exclamation-circle"
        subHeader="You are about to export all filtered records as .CSV File."
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone.',
        ]}
        handleClose={() =>
          setModalState(s => ({ ...s, confirmDownload: false }))
        }
        handleCancel={() =>
          setModalState(s => ({ ...s, confirmDownload: false }))
        }
        confirmText="Yes"
        handleConfirm={handleDownloadConfirm}
      />
      <AlertModal
        isOpen={modalState.successDownload}
        title="Export Reports Alert"
        header="SUCCESS!"
        variant="success"
        icon="check-circle"
        subHeader={
          <span>
            You exported{' '}
            <TransactionLogHighlight>
              {data.length} row(s)
            </TransactionLogHighlight>{' '}
            as .CSV File succesfully.
          </span>
        }
        description="Kindly check the downloaded file."
        confirmText="Back to All Reports"
        handleClose={() =>
          setModalState(s => ({ ...s, successDownload: false }))
        }
        handleConfirm={() =>
          setModalState(s => ({ ...s, successDownload: false }))
        }
      />
      <AlertModal
        isOpen={modalState.fileInvalid}
        title="Upload Alert"
        header="Upload Error!"
        variant="error"
        icon="times-circle"
        subHeader={<span>{modalState.uploadErrorMessage}</span>}
        description="Kindly check the uploaded file."
        confirmText="Back to All Reports"
        handleClose={() => {
          setModalState(s => ({
            ...s,
            fileInvalid: false,
            uploadErrorMessage: '',
          }));
          setIsBulkUpload(false);
          setFile(null);
        }}
        handleConfirm={() => {
          setModalState(s => ({
            ...s,
            fileInvalid: false,
            uploadErrorMessage: '',
          }));
          setIsBulkUpload(false);
          setFile(null);
        }}
      />
    </>
  );
};

TransactionLogs.propTypes = {};

export default TransactionLogs;
