import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import React, { useContext, useState } from 'react';
import { useQuery, useMutation } from '@apollo/client';

// Components
import { ExportButton } from '../../../components/Button/ExportButton';
import FileButton from '../../../components/Button/FileButton';
import DataContainer from '../../../components/DataContainer';
import DataTable from '../../../components/DataTable';
import { FIELD_TYPES } from '../../../components/Form/constants';
import GlobalSearch from '../../../components/GlobalSearch';
import Header from '../../../components/Header';
import { AlertModal } from '../../../components/Modal';
import Page from '../../../components/Page';

// Hooks & Context
import AuthContext from '../../../context/AuthContext/AuthContext';
import NotificationContext from '../../../context/NotificationContext';
// IMPORTANT: Make sure you are importing the new, refactored hook
import useQueryReportSeries from '../../../hooks/useQueryReportSeries-v3';
// Assuming this hook is also refactored to the new pattern
import useQueryReportSeriesWithFile from '../../../hooks/useQueryReportSeriesWithFile';

// Utils & Styled Components
import formatCurrency from '../../../utils/formatCurrency';
import formatUtcDate from '../../../utils/formatUtcDate';
import {
  ColumnVisibilityDropdown,
  ResponsiveRow,
  TransactionLogHighlight,
} from '../styled';
import { json2CSVYTD } from '../../../components/GlobalSearch/utils';

// Queries & Mutations
import { EXPORT_REPORTS } from '../mutation';
import {
  GET_CHANNEL_OPTIONS,
  GET_TRANSACTION_LOGS_INFO,
  BULKUPLOAD,
  REPORT_PATH,
} from './query';

const PAYMENT_GATEWAYS = [
  { label: 'GCash', value: 'gcash' },
  { label: 'Xendit', value: 'xendit' },
];

const TransactionLogs = () => {
  const { reportPermissions } = useContext(AuthContext);
  const { addNotif } = useContext(NotificationContext);

  // --- State Management ---
  const [filter, setFilter] = useState({});
  const [isBulkUpload, setIsBulkUpload] = useState(false);
  const [file, setFile] = useState(null);

  // --- Data Fetching (Regular Search) ---
  const { data, loading, error, loadMore, isLastPage } = useQueryReportSeries(
    GET_TRANSACTION_LOGS_INFO,
    REPORT_PATH,
    {
      filter,
      pagination: { limit: 10 },
    }
  );

  // --- Data Fetching (Bulk Search) ---
  const {
    data: transactionData,
    loading: isLoadingTransactionData,
    loadMore: loadMoreBulk,
    isLastPage: isBulkLastPage,
    error: bulkError,
  } = useQueryReportSeriesWithFile(BULKUPLOAD, 'uploadTransactions', {
    file,
    pagination: { limit: 10 },
  });

  const { data: channelData, loading: isLoadingChannels } =
    useQuery(GET_CHANNEL_OPTIONS);

  const { data: transactionDataDownload } = useQuery(BULKUPLOAD, {
    variables: { file, pagination: { limit: 1000 } },
    fetchPolicy: 'network-only',
    skip: !file,
  });

  const [logExtraction] = useMutation(EXPORT_REPORTS);

  const [visibleColumns, setVisibleColumns] = useState([
    'paymentId',
    'createDateTime',
    'channelId',
    'customerId',
    'customerName',
    'gatewayProcessor',
    'paymentMethod',
    'sessionId',
    'status',
    'totalAmount',
  ]);

  const [modalState, setModalState] = useState({
    confirmDownload: false,
    successDownload: false,
    fileInvalid: false,
    uploadErrorMessage: '',
  });

  const channelOptions =
    !isLoadingChannels && channelData
      ? [
          { value: null, label: 'Any' },
          ...channelData.channelsLoose.map(channel => ({
            value: channel.id,
            label: channel.name,
          })),
        ]
      : [{ value: null, label: 'Any' }];

  const globalSearchFields = [
    {
      label: 'Payment ID',
      name: 'paymentId',
      type: FIELD_TYPES.TEXT,
      isKey: true,
    },
    { label: 'Payment Code', name: 'paymentCode', type: FIELD_TYPES.TEXT },
    {
      label: 'Account No.',
      name: 'accountId',
      type: FIELD_TYPES.TEXT,
      isKey: true,
    },
    {
      label: 'Channel Name',
      name: 'channelId',
      type: FIELD_TYPES.SELECT,
      options: channelOptions,
      isKey: true,
    },
    { label: 'Email Address', name: 'email', type: FIELD_TYPES.TEXT },
    {
      label: 'Payment Method',
      name: 'paymentMethod',
      type: FIELD_TYPES.SELECT,
      options: [
        { value: null, label: 'Any' },
        { value: 'gcash', label: 'GCash' },
        { value: 'otc', label: 'Xendit OTC' },
        { value: 'card_straight', label: 'Xendit CC/DC Straight' },
        { value: 'card_installment', label: 'Xendit CC/DC Installment' },
      ],
    },
    {
      label: 'Status',
      name: 'status',
      type: FIELD_TYPES.SELECT,
      options: [
        { value: null, label: 'Any' },
        { value: 'ECPAY_GENERATED', label: 'ECPAY_GENERATED' },
        { value: 'ECPAY_SESSION_CREATED', label: 'ECPAY_SESSION_CREATED' },
        { value: 'POSTED', label: 'PAYMENT_POSTED' },
        { value: 'ECPAY_AUTHORISED', label: 'ECPAY_AUTHORISED' },
        { value: 'ECPAY_EXPIRED', label: 'ECPAY_EXPIRED' },
        { value: 'WEB_SESSION_CREATED', label: 'WEB_SESSION_CREATED' },
        {
          value: 'XENDIT_CALLBACK_RECEIVED',
          label: 'XENDIT_CALLBACK_RECEIVED',
        },
      ],
    },
    {
      label: 'Total Amount',
      name: 'totalAmount',
      type: FIELD_TYPES.NUMBER,
      noOperator: true,
    },
    {
      label: 'Settlement Amount',
      name: 'amount',
      type: FIELD_TYPES.NUMBER,
      noOperator: true,
    },
    {
      label: 'Mobile Number',
      name: 'mobile',
      type: FIELD_TYPES.NUMBER,
      noOperator: true,
    },
    { label: 'Date', name: 'createDateTime', type: FIELD_TYPES.DATE_RANGE },
    {
      label: 'Payment Gateway',
      name: 'gatewayProcessor',
      type: FIELD_TYPES.SELECT,
      options: PAYMENT_GATEWAYS,
    },
    {
      label: 'Bill Type',
      name: 'transactionType',
      type: FIELD_TYPES.SELECT,
      options: [
        { value: null, label: 'Any' },
        { value: 'G', label: 'Bill Type' },
        { value: 'N', label: 'Non-Bill Type' },
      ],
    },
  ];

  const tableConfig = {
    paymentId: { headerLabel: 'Payment ID', sortable: true },
    createDateTime: {
      headerLabel: 'Created Date',
      sortable: true,
      renderAs: data => formatUtcDate(data.createDateTime?.replace('Z', '')),
    },
    channelId: {
      headerLabel: 'Channel',
      sortable: true,
      renderAs: data =>
        !isLoadingChannels && channelData
          ? channelData.channelsLoose.find(
              channel => channel.id === data.channelId
            )?.name || data.channelId
          : data.channelId,
    },
    customerId: { headerLabel: 'Customer ID', sortable: true },
    customerName: { headerLabel: 'Customer Name', sortable: true },
    gatewayProcessor: { headerLabel: 'Gateway Processor', sortable: true },
    paymentMethod: { headerLabel: 'Payment Method', sortable: true },
    sessionId: { headerLabel: 'Session ID', sortable: true },
    status: { headerLabel: 'Status', sortable: true },
    totalAmount: {
      headerLabel: 'Total Amount',
      sortable: true,
      renderAs: data => formatCurrency(data.totalAmount, true),
    },
    oona: {
      headerLabel: 'Oona',
      sortable: true,
      renderAs: data => formatCurrency(data.oona, true),
    },
    budgetProtect: {
      headerLabel: 'Budget Protect',
      sortable: true,
      renderAs: data => formatCurrency(data.budgetProtect, true),
    },
    convenienceFeeAmount: {
      headerLabel: 'Convenience Fee Amount',
      sortable: true,
      renderAs: data => formatCurrency(data.convenienceFeeAmount, true),
    },
    accountId: {
      headerLabel: 'Account ID',
      sortable: true,
      renderAs: data => data.settlement?.accountId || '-',
    },
    accountType: {
      headerLabel: 'Account Type',
      sortable: true,
      renderAs: data => data.settlement?.accountType || '-',
    },
    amount: {
      headerLabel: 'Settlement Amount',
      sortable: true,
      renderAs: data =>
        data.settlement?.amount
          ? formatCurrency(data.settlement?.amount, true)
          : '-',
    },
    brand: {
      headerLabel: 'Brand',
      sortable: true,
      renderAs: data => data.settlement?.brand || '-',
    },
    email: {
      headerLabel: 'Email',
      sortable: true,
      renderAs: data => data.settlement?.email || '-',
    },
    mobile: {
      headerLabel: 'Mobile Number',
      sortable: true,
      renderAs: data => data.settlement?.mobile || '-',
    },
    transactionType: {
      headerLabel: 'Settlement Txn Type',
      sortable: true,
      renderAs: data => {
        if (data.settlement?.transactionType === 'G') return 'Bill';
        if (data.settlement?.transactionType === 'N') return 'Non-Bill';
        return data.settlement?.transactionType || '-';
      },
    },
    settlementStatus: {
      headerLabel: 'Settlement Status',
      sortable: true,
      renderAs: data => data.settlement?.status || '-',
    },
    refundId: { headerLabel: 'Refund ID', sortable: true },
    refundApprovalStatus: { headerLabel: 'Refund Status', sortable: true },
    refundReason: { headerLabel: 'Refund Reason', sortable: true },
    refundAmount: {
      headerLabel: 'Refund Amount',
      sortable: true,
      renderAs: data => formatCurrency(data.refundAmount, true),
    },
  };

  const handleBulkReset = () => {
    setIsBulkUpload(false);
    setFile(null);
  };

  const handleBulkUpload = uploadedFile => {
    const extension = uploadedFile.name.split('.').pop();
    if (extension !== 'csv') {
      setModalState({
        fileInvalid: true,
        uploadErrorMessage: 'File must be a CSV',
      });
      return;
    }
    if (uploadedFile.size > 5000000) {
      setModalState({
        fileInvalid: true,
        uploadErrorMessage: 'File must not be more than 5mb',
      });
      return;
    }
    setFile(uploadedFile);
    setIsBulkUpload(true);
  };

  const handleDownloadConfirm = async () => {
    setModalState(s => ({ ...s, confirmDownload: false }));
    if (!isBulkUpload) {
      const notifTime = new Date().getTime();
      addNotif({
        id: `TRANS-${notifTime}`,
        notifTime,
        type: 'info',
        title: 'Downloading Report File',
        message: 'Downloading Transaction Logs Report',
        isProgress: true,
        progressData: { progress: 0, isProgressive: false },
        isLocal: true,
        reportDLParams: {
          query: GET_TRANSACTION_LOGS_INFO,
          path: REPORT_PATH,
          variables: { filter, pagination: { limit: 1000 } },
          onDownload: () =>
            logExtraction({ variables: { data: { type: 'transaction' } } }),
          tableConfig,
          fileName: 'transaction-logs.csv',
        },
      });
    } else {
      const csvData = await json2CSVYTD(
        transactionDataDownload?.uploadTransactions?.filteredData || [],
        tableConfig
      );
      const blob = new Blob([csvData], { type: 'text/csv' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'transaction-logs.csv';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    }
  };

  const currentData = isBulkUpload ? transactionData : data;
  const currentLoading = isBulkUpload ? isLoadingTransactionData : loading;
  const currentLoadMore = isBulkUpload ? loadMoreBulk : loadMore;
  const currentIsLastPage = isBulkUpload ? isBulkLastPage : isLastPage;

  return (
    <>
      <Page>
        <Header
          withHome
          title="Transaction Logs"
          path={['Reports', 'Transaction Logs']}
        />
        <DataContainer>
          <DataTable
            minCellWidth={200}
            headerOptions={
              <>
                <GlobalSearch
                  onSearch={setFilter}
                  fields={globalSearchFields}
                />
                <ResponsiveRow>
                  {isBulkUpload ? (
                    <ExportButton
                      icon="times"
                      iconPosition="left"
                      onClick={handleBulkReset}
                    >
                      Reset Bulk Search
                    </ExportButton>
                  ) : (
                    reportPermissions.Transaction.import && (
                      <FileButton
                        disabled={loading}
                        loading={loading}
                        onImport={handleBulkUpload}
                        onTemplate={() => {
                          const fileData = {
                            mime: 'text/csv',
                            filename: 'bulk-search-transaction-logs.csv',
                            contents:
                              'reference,accountNumber,date from,date to\nTEST1591718837100693,*********,04/23/2021,04/23/2021\n',
                          };
                          const blob = new Blob([fileData.contents], {
                            type: fileData.mime,
                          });
                          const url = URL.createObjectURL(blob);
                          const a = document.createElement('a');
                          a.href = url;
                          a.download = fileData.filename;
                          document.body.appendChild(a);
                          a.click();
                          document.body.removeChild(a);
                        }}
                      >
                        Bulk Search{' '}
                        <FontAwesomeIcon
                          style={{ marginLeft: 10 }}
                          icon="angle-down"
                        />
                      </FileButton>
                    )
                  )}
                  {reportPermissions.Transaction.export && (
                    <ExportButton
                      icon="file-csv"
                      iconPosition="left"
                      disabled={currentLoading}
                      onClick={() =>
                        setModalState(s => ({ ...s, confirmDownload: true }))
                      }
                    >
                      CSV
                    </ExportButton>
                  )}
                  <ColumnVisibilityDropdown
                    multi
                    placeholder="Visible Columns"
                    value={visibleColumns}
                    options={Object.keys(tableConfig).map(key => ({
                      value: key,
                      label: tableConfig[key].headerLabel,
                    }))}
                    onChange={setVisibleColumns}
                  />
                </ResponsiveRow>
              </>
            }
            loading={currentLoading}
            data={currentData}
            config={Object.keys(tableConfig).reduce((config, key) => {
              if (visibleColumns.includes(key)) config[key] = tableConfig[key];
              return config;
            }, {})}
            footer={
              !currentIsLastPage && (
                <div style={{ textAlign: 'center', padding: '20px' }}>
                  <ExportButton
                    onClick={currentLoadMore}
                    disabled={currentLoading}
                  >
                    {currentLoading ? 'Loading...' : 'Load More'}
                  </ExportButton>
                </div>
              )
            }
          />
        </DataContainer>
      </Page>
      <AlertModal
        isOpen={modalState.confirmDownload}
        title="Export Reports Alert"
        header="ARE YOU SURE?"
        variant="warn"
        icon="exclamation-circle"
        subHeader="You are about to export all filtered records as .CSV File."
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone.',
        ]}
        handleClose={() =>
          setModalState(s => ({ ...s, confirmDownload: false }))
        }
        handleCancel={() =>
          setModalState(s => ({ ...s, confirmDownload: false }))
        }
        confirmText="Yes"
        handleConfirm={handleDownloadConfirm}
      />
      <AlertModal
        isOpen={modalState.successDownload}
        title="Export Reports Alert"
        header="SUCCESS!"
        variant="success"
        icon="check-circle"
        subHeader={
          <span>
            You exported{' '}
            <TransactionLogHighlight>
              {data.length} row(s)
            </TransactionLogHighlight>{' '}
            as .CSV File succesfully.
          </span>
        }
        description="Kindly check the downloaded file."
        confirmText="Back to All Reports"
        handleClose={() =>
          setModalState(s => ({ ...s, successDownload: false }))
        }
        handleConfirm={() =>
          setModalState(s => ({ ...s, successDownload: false }))
        }
      />
      <AlertModal
        isOpen={modalState.fileInvalid}
        title="Upload Alert"
        header="Upload Error!"
        variant="error"
        icon="times-circle"
        subHeader={<span>{modalState.uploadErrorMessage}</span>}
        description="Kindly check the uploaded file."
        confirmText="Back to All Reports"
        handleClose={() => {
          setModalState(s => ({
            ...s,
            fileInvalid: false,
            uploadErrorMessage: '',
          }));
          handleBulkReset();
        }}
        handleConfirm={() => {
          setModalState(s => ({
            ...s,
            fileInvalid: false,
            uploadErrorMessage: '',
          }));
          handleBulkReset();
        }}
      />
    </>
  );
};

TransactionLogs.propTypes = {};

export default TransactionLogs;
