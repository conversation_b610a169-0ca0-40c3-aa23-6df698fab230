import format from 'date-fns/format';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import styled from 'styled-components';
import React, { useContext, useEffect, useState } from 'react';
import { useQuery } from '@apollo/client';
import { ExportButton } from '../../../components/Button/ExportButton';
import DataContainer from '../../../components/DataContainer';
import DataTable from '../../../components/DataTable';
import { FIELD_TYPES } from '../../../components/Form/constants';
import GlobalSearch from '../../../components/GlobalSearch';
import Header from '../../../components/Header';
import { AlertModal } from '../../../components/Modal';
import Page from '../../../components/Page';
import AuthContext from '../../../context/AuthContext/AuthContext';
import { useMutation } from '@apollo/client';
import useQueryReportSeries from '../../../hooks/useQueryReportSeries-v2';
import useQueryReportSeriesWithFile from '../../../hooks/useQueryReportSeriesWithFile';
import formatCurrency from '../../../utils/formatCurrency';
import formatUtcDate from '../../../utils/formatUtcDate';
import FileButton from '../../../components/Button/FileButton';
import { EXPORT_REPORTS } from '../mutation';
import {
  ColumnVisibilityDropdown,
  ResponsiveRow,
  TransactionLogHighlight,
} from '../styled';
import {
  GET_CHANNEL_OPTIONS,
  GET_TRANSACTION_LOGS_INFO,
  BULKUPLOAD,
  REPORT_PATH,
} from './query';
import NotificationContext from '../../../context/NotificationContext';
import { BILL_TYPES } from '../../MID/MID';
import {
  numberWithCommas,
  json2CSVYTD,
} from '../../../components/GlobalSearch/utils';

const BulkSearchButton = styled.div``;

const PAYMENT_GATEWAYS = [
  { label: 'GCash', value: 'gcash' },
  { label: 'Xendit', value: 'xendit' },
];

const TransactionLogs = () => {
  const { reportPermissions } = useContext(AuthContext);
  const {
    pagination,
    setNewPagination,
    filter,
    setFilter,
    data,
    loading,
    page,
    setPage,
    isLastPage,
  } = useQueryReportSeries(GET_TRANSACTION_LOGS_INFO, REPORT_PATH, {
    pagination: {
      startKey: '',
      limit: 10,
    },
  });

  const {
    pagination: bulkPagination,
    setNewPagination: setNewBulkPagination,
    setFile,
    file,
    data: transactionData,
    loading: isLoadingTransactionData,
    page: bulkPage,
    setPage: setBulkPage,
    isLastPage: isBulkLastPage,
    loadData,
    error,
  } = useQueryReportSeriesWithFile(BULKUPLOAD, 'uploadTransactions', {
    pagination: {
      startKey: '',
      limit: 10,
    },
  });

  const { addNotif } = useContext(NotificationContext);

  const [isConfirmDownloadModalOpen, setIsConfirmDownloadModalOpen] =
    useState(false);
  const [isSuccessDownloadModalOpen, setIsSuccessDownloadModalOpen] =
    useState(false);

  const [isFileInvalid, setIsFileInvalid] = useState(false);

  const { data: channelData, loading: isLoadingChannels } =
    useQuery(GET_CHANNEL_OPTIONS);

  const { data: transactionDataDownload } = useQuery(BULKUPLOAD, {
    variables: {
      file: file,
      pagination: {
        limit: 1000,
      },
    },
    fetchPolicy: 'networkOnly',
    skip: !file,
  });

  const [logExtraction] = useMutation(EXPORT_REPORTS);

  const [isBulkUpload, setIsBulkUpload] = useState(false);

  const [state, setState] = useState({
    uploadErrorMessage: '',
  });

  const channelOptions =
    !isLoadingChannels && channelData
      ? channelData.channelsLoose.map(channel => ({
          value: channel.id,
          label: channel.name,
        }))
      : [];

  channelOptions.unshift({
    value: null,
    label: 'Any',
  });

  const tableConfig = {
    paymentId: {
      headerLabel: 'Payment ID',
      sortable: true,
    },
    createDateTime: {
      headerLabel: 'Created Date',
      sortable: true,
      renderAs: data => formatUtcDate(data.createDateTime?.replace('Z', '')),
    },
    channelId: {
      headerLabel: 'Channel',
      sortable: true,
      renderAs: data => {
        return !isLoadingChannels && channelData
          ? channelData.channelsLoose.find(
              channel => channel.id === data.channelId
            )?.name || data.channelId
          : data.channelId;
      },
    },
    customerId: {
      headerLabel: 'Customer ID',
      sortable: true,
    },
    customerName: {
      headerLabel: 'Customer Name',
      sortable: true,
    },
    gatewayProcessor: {
      headerLabel: 'Gateway Processor',
      sortable: true,
    },
    paymentMethod: {
      headerLabel: 'Payment Method',
      sortable: true,
    },
    sessionId: {
      headerLabel: 'Session ID',
      sortable: true,
    },
    status: {
      headerLabel: 'Status',
      sortable: true,
    },
    totalAmount: {
      headerLabel: 'Total Amount',
      sortable: true,
      renderAs: data => formatCurrency(data.totalAmount, true),
    },
    oona: {
      headerLabel: 'Oona',
      sortable: true,
      renderAs: data => formatCurrency(data.oona, true),
    },
    budgetProtect: {
      headerLabel: 'Budget Protect',
      sortable: true,
      renderAs: data => formatCurrency(data.budgetProtect, true),
    },
    convenienceFeeAmount: {
      headerLabel: 'Convenience Fee Amount',
      sortable: true,
      renderAs: data => formatCurrency(data.convenienceFeeAmount, true),
    },
    accountId: {
      headerLabel: 'Account ID',
      sortable: true,
      renderAs: data => data.settlement?.accountId || '-',
    },
    accountType: {
      headerLabel: 'Account Type',
      sortable: true,
      renderAs: data => data.settlement?.accountType || '-',
    },
    amount: {
      headerLabel: 'Settlement Amount',
      sortable: true,
      renderAs: data =>
        data.settlement?.amount
          ? '-'
          : formatCurrency(data.settlement?.amount, true),
    },
    brand: {
      headerLabel: 'Brand',
      sortable: true,
      renderAs: data => data.settlement?.brand || '-',
    },
    email: {
      headerLabel: 'Email',
      sortable: true,
      renderAs: data => data.settlement?.email || '-',
    },
    mobile: {
      headerLabel: 'Mobile Number',
      sortable: true,
      renderAs: data => data.settlement?.mobile || '-',
    },
    transactionType: {
      headerLabel: 'Settlement Txn Type',
      sortable: true,
      renderAs: data => {
        if (data.settlement?.transactionType === 'G') return 'Bill';
        if (data.settlement?.transactionType === 'N') return 'Non-Bill';
        return data.settlement?.transactionType || '-';
      },
    },
    settlementStatus: {
      headerLabel: 'Settlement Status',
      sortable: true,
      renderAs: data => data.settlement?.status || '-',
    },
    refundId: {
      headerLabel: 'Refund ID',
      sortable: true,
    },
    refundApprovalStatus: {
      headerLabel: 'Refund Status',
      sortable: true,
    },
    refundReason: {
      headerLabel: 'Refund Reason',
      sortable: true,
    },
    refundAmount: {
      headerLabel: 'Refund Amount',
      sortable: true,
      renderAs: data => formatCurrency(data.refundAmount, true),
    },
  };

  useEffect(() => {
    if (isLoadingTransactionData) {
      loadData();
    }
  }, []);

  useEffect(() => {
    if (
      isLoadingTransactionData &&
      error &&
      error.networkError.result &&
      error.networkError.result.message === 'column_mismatched'
    ) {
      setState({
        ...state,
        uploadErrorMessage: 'Column Mismatched',
      });
      setIsFileInvalid(true);
    }
  }, [error]);

  const [visibleColumns, setVisibleColumns] = useState(
    Object.keys(tableConfig)
  );

  return (
    <>
      <Page>
        <Header
          withHome
          title="Transaction Logs"
          path={['Reports', 'Transaction Logs']}
        />
        <DataContainer>
          <DataTable
            minCellWidth={200}
            headerOptions={
              <>
                <GlobalSearch
                  onSearch={filter => {
                    const newPagination = {
                      ...pagination,
                      startKey: '',
                    };
                    setFilter(filter);
                    setNewPagination(newPagination);
                  }}
                  fields={[
                    {
                      label: 'Payment ID',
                      name: 'paymentId',
                      type: FIELD_TYPES.TEXT,
                      isKey: true,
                    },
                    {
                      label: 'Payment Code',
                      name: 'paymentCode',
                      type: FIELD_TYPES.TEXT,
                    },
                    {
                      label: 'Account No.',
                      name: 'accountId',
                      type: FIELD_TYPES.TEXT,
                      isKey: true,
                    },
                    {
                      label: 'Channel Name',
                      name: 'channelId',
                      type: FIELD_TYPES.SELECT,
                      options: channelOptions,
                      isKey: true,
                    },
                    {
                      label: 'Email Address',
                      name: 'email',
                      type: FIELD_TYPES.TEXT,
                    },
                    {
                      label: 'Payment Method',
                      name: 'paymentMethod',
                      type: FIELD_TYPES.SELECT,
                      options: [
                        { value: null, label: 'Any' },
                        { value: 'gcash', label: 'GCash' },
                        { value: 'otc', label: 'Xendit OTC' },
                        {
                          value: 'card_straight',
                          label: 'Xendit CC/DC Straight',
                        },
                        {
                          value: 'card_installment',
                          label: 'Xendit CC/DC Installment',
                        },
                      ],
                      disabled: ({
                        prodDesc,
                        status,
                        amountCurrency,
                        amountValue,
                        mobileNumber,
                      }) =>
                        !!(
                          prodDesc ||
                          status ||
                          amountCurrency ||
                          amountValue ||
                          mobileNumber
                        ),
                    },
                    {
                      label: 'Status',
                      name: 'status',
                      type: FIELD_TYPES.SELECT,
                      disabled: ({
                        prodDesc,
                        paymentMethod,
                        amountCurrency,
                        amountValue,
                        mobileNumber,
                      }) =>
                        !!(
                          prodDesc ||
                          paymentMethod ||
                          amountCurrency ||
                          amountValue ||
                          mobileNumber
                        ),
                      options: [
                        { value: null, label: 'Any' },
                        {
                          value: 'ECPAY_GENERATED',
                          label: 'ECPAY_GENERATED',
                        },
                        {
                          value: 'ECPAY_SESSION_CREATED',
                          label: 'ECPAY_SESSION_CREATED',
                        },
                        { value: 'POSTED', label: 'PAYMENT_POSTED' },
                        {
                          value: 'ECPAY_AUTHORISED',
                          label: 'ECPAY_AUTHORISED',
                        },
                        {
                          value: 'ECPAY_EXPIRED',
                          label: 'ECPAY_EXPIRED',
                        },
                        {
                          value: 'WEB_SESSION_CREATED',
                          label: 'WEB_SESSION_CREATED',
                        },
                        {
                          value: 'XENDIT_CALLBACK_RECEIVED',
                          label: 'XENDIT_CALLBACK_RECEIVED',
                        },
                      ],
                    },
                    {
                      label: 'Total Amount',
                      name: 'totalAmount',
                      type: FIELD_TYPES.NUMBER,
                      noOperator: true,
                      disabled: ({
                        prodDesc,
                        status,
                        amountCurrency,
                        paymentMethod,
                        mobileNumber,
                        amountValue,
                      }) =>
                        !!(
                          prodDesc ||
                          paymentMethod ||
                          amountCurrency ||
                          status ||
                          mobileNumber ||
                          amountValue
                        ),
                    },
                    {
                      label: 'Settlement Amount',
                      name: 'amount',
                      type: FIELD_TYPES.NUMBER,
                      noOperator: true,
                      disabled: ({
                        prodDesc,
                        status,
                        amountCurrency,
                        paymentMethod,
                        mobileNumber,
                        totalAmount,
                      }) =>
                        !!(
                          prodDesc ||
                          paymentMethod ||
                          amountCurrency ||
                          status ||
                          mobileNumber ||
                          totalAmount
                        ),
                    },
                    {
                      label: 'Mobile Number',
                      name: 'mobile',
                      type: FIELD_TYPES.NUMBER,
                      noOperator: true,
                      disabled: ({
                        prodDesc,
                        status,
                        amountCurrency,
                        amountValue,
                        paymentMethod,
                      }) =>
                        !!(
                          prodDesc ||
                          status ||
                          amountCurrency ||
                          amountValue ||
                          paymentMethod
                        ),
                    },
                    {
                      label: 'Date',
                      name: 'createDateTime',
                      type: FIELD_TYPES.DATE_RANGE,
                    },
                    {
                      label: 'Payment Gateway',
                      name: 'gatewayProcessor',
                      type: FIELD_TYPES.SELECT,
                      options: PAYMENT_GATEWAYS,
                    },
                    {
                      label: 'Bill Type',
                      name: 'transactionType',
                      type: FIELD_TYPES.SELECT,
                      options: [
                        { value: null, label: 'Any' },
                        { value: 'G', label: 'Bill Type' },
                        { value: 'N', label: 'Non-Bill Type' },
                      ],
                    },
                  ]}
                />
                <ResponsiveRow>
                  {isBulkUpload ? (
                    <BulkSearchButton>
                      <ExportButton
                        icon="times"
                        iconPosition="left"
                        disabled={loading}
                        onClick={() => {
                          setIsBulkUpload(false);
                          setFile(null);
                          setBulkPage(1);
                        }}
                      >
                        Reset Bulk Search
                      </ExportButton>
                    </BulkSearchButton>
                  ) : (
                    <BulkSearchButton>
                      {reportPermissions.Transaction.import && (
                        <FileButton
                          disabled={loading}
                          loading={loading}
                          onImport={
                            reportPermissions.Transaction.import &&
                            (file => {
                              let fileName = file.name.split('.').pop();
                              var reader = new FileReader();
                              reader.onload = function () {
                                var lines = this.result.split('\r').length;
                                if (file.size > 5000000) {
                                  setState({
                                    ...state,
                                    uploadErrorMessage:
                                      'File must not be more than 5mb',
                                  });
                                  setIsFileInvalid(true);
                                } else if (fileName !== 'csv') {
                                  setState({
                                    ...state,
                                    uploadErrorMessage: 'File must be a CSV',
                                  });
                                  setIsFileInvalid(true);
                                } else if (lines > 1001) {
                                  setState({
                                    ...state,
                                    uploadErrorMessage:
                                      'CSV Row Data Exceed to 1000 rows',
                                  });
                                  setIsFileInvalid(true);
                                } else {
                                  setFile(file);
                                  setIsBulkUpload(true);
                                  loadData();
                                }
                              };
                              reader.readAsText(file);
                            })
                          }
                          onTemplate={() => {
                            const fileData = {
                              mime: 'text/csv',
                              filename: 'bulk-search-transaction-logs.csv',
                              contents:
                                'reference,accountNumber,date from,date to\nTEST1591718837100693,*********,04/23/2021,04/23/2021\n',
                            };
                            const blob = new Blob([fileData.contents], {
                              type: fileData.mime,
                            });
                            const url = URL.createObjectURL(blob);
                            const a = document.createElement('a');
                            document.body.appendChild(a);
                            a.download = fileData.filename;
                            a.href = url;
                            a.click();
                            document.body.removeChild(a);
                          }}
                        >
                          Bulk Search{' '}
                          <FontAwesomeIcon
                            style={{ marginLeft: 10 }}
                            icon="angle-down"
                          />
                        </FileButton>
                      )}
                    </BulkSearchButton>
                  )}
                  {reportPermissions.Transaction.export && (
                    <ExportButton
                      icon="file-csv"
                      iconPosition="left"
                      disabled={loading}
                      onClick={() => setIsConfirmDownloadModalOpen(true)}
                    >
                      CSV
                    </ExportButton>
                  )}
                  <ColumnVisibilityDropdown
                    multi
                    showMulti={false}
                    placeholder="Visible Columns"
                    value={visibleColumns}
                    options={Object.keys(tableConfig).map(key => ({
                      value: key,
                      label: tableConfig[key].headerLabel,
                    }))}
                    onChange={setVisibleColumns}
                  />
                </ResponsiveRow>
              </>
            }
            loading={isBulkUpload ? isLoadingTransactionData : loading}
            data={isBulkUpload ? transactionData : data}
            config={Object.keys(tableConfig).reduce((config, key) => {
              if (visibleColumns.includes(key)) {
                config[key] = tableConfig[key];
              }
              return config;
            }, {})}
            pagination={
              isBulkUpload
                ? {
                    ...bulkPagination,
                    start: bulkPagination.startKey,
                    withStartKeys: true,
                    count: 0,
                    cursors: [],
                    handleChange: setNewBulkPagination,
                  }
                : {
                    ...pagination,
                    start: pagination.startKey,
                    withStartKeys: true,
                    count: 0,
                    cursors: [],
                    handleChange: setNewPagination,
                  }
            }
            series={
              isBulkUpload
                ? {
                    page: bulkPage,
                    setPage: setBulkPage,
                    isLastPage: isBulkLastPage,
                  }
                : { page, setPage, isLastPage }
            }
          />
        </DataContainer>
      </Page>
      <AlertModal
        isOpen={isConfirmDownloadModalOpen}
        title="Export Reports Alert"
        header="ARE YOU SURE?"
        variant="warn"
        icon="exclamation-circle"
        subHeader={
          <span>
            You are about to export all filtered records as .CSV File.
          </span>
        }
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone.',
        ]}
        handleClose={() => setIsConfirmDownloadModalOpen(false)}
        handleCancel={() => setIsConfirmDownloadModalOpen(false)}
        confirmText="Yes"
        handleConfirm={async () => {
          if (!isBulkUpload) {
            const notifTime = new Date().getTime();
            setIsConfirmDownloadModalOpen(false);
            addNotif({
              id: 'TRANS-' + notifTime,
              notifTime,
              type: 'info',
              title: 'Downloading Report File',
              message: 'Downloading Transaction Logs Report',
              isProgress: true,
              progressData: {
                progress: 0,
                isProgressive: false,
              },
              isLocal: true,
              data: {},
              reportDLParams: {
                query: GET_TRANSACTION_LOGS_INFO,
                path: REPORT_PATH,
                variables: {
                  filter,
                  pagination: {
                    startKey: '',
                    limit: 1000,
                  },
                },
                onDownload: () => {
                  logExtraction({
                    variables: {
                      data: {
                        type: 'transaction',
                      },
                    },
                  });
                },
                tableConfig,
                fileName: 'transaction-logs.csv',
              },
            });
          } else if (isBulkUpload) {
            setIsConfirmDownloadModalOpen(false);
            const TransactionLogs = await json2CSVYTD(
              transactionDataDownload &&
                transactionDataDownload.uploadTransactions && [
                  ...transactionDataDownload.uploadTransactions.filteredData,
                ],
              tableConfig
            );
            let csvData = `${TransactionLogs}\n,\n`;
            const fileData = {
              mime: 'text/csv',
              filename: `transaction-logs.csv`,
              contents: csvData,
            };
            const blob = new Blob([fileData.contents], {
              type: fileData.mime,
            });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            document.body.appendChild(a);
            a.download = fileData.filename;
            a.href = url;
            a.click();
            document.body.removeChild(a);
          }
        }}
      />
      <AlertModal
        isOpen={isSuccessDownloadModalOpen}
        title="Export Reports Alert"
        header="SUCCESS!"
        variant="success"
        icon="check-circle"
        subHeader={
          <span>
            You exported{' '}
            <TransactionLogHighlight>
              {data.length} row(s){' '}
            </TransactionLogHighlight>{' '}
            as .CSV File succesfully.
          </span>
        }
        description="Kindly check the downloaded file."
        confirmText="Back to All Reports"
        handleClose={() => setIsSuccessDownloadModalOpen(false)}
        handleConfirm={() => {
          setIsSuccessDownloadModalOpen(false);
        }}
      />
      <AlertModal
        isOpen={isFileInvalid}
        title="Upload Alert"
        header="Upload Error!"
        variant="error"
        icon="times-circle"
        subHeader={<span>{state.uploadErrorMessage}</span>}
        description="Kindly check the uploaded file."
        confirmText="Back to All Reports"
        handleClose={() => {
          setIsFileInvalid(false);
          setIsBulkUpload(false);
          setFile(null);
        }}
        handleConfirm={() => {
          setIsFileInvalid(false);
          setIsBulkUpload(false);
          setFile(null);
        }}
      />
      );
    </>
  );
};

TransactionLogs.propTypes = {};

export default TransactionLogs;
