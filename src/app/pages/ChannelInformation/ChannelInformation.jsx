import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import addDays from 'date-fns/add_days';
import addMonths from 'date-fns/add_months';
import startOfDay from 'date-fns/start_of_day';
import PropTypes from 'prop-types';
import React, { useContext, useEffect, useMemo, useState } from 'react';
import { useQuery } from '@apollo/client';
import styled from 'styled-components';
import * as Yup from 'yup';
import PrimaryButton from '../../components/Button/PrimaryButton';
import SecondaryButton from '../../components/Button/SecondaryButton';
import DataContainer from '../../components/DataContainer';
import DataHeader from '../../components/DataHeader';
import DateRange from '../../components/DateRange';
import { FIELD_TYPES } from '../../components/Form/constants';
import FormField, {
  FormFieldLabel,
  FormFieldStatic,
  StyledFormField,
} from '../../components/Form/FormField';
import Header from '../../components/Header';
import {
  ButtonsContainer,
  PageSubsection,
  SubsectionTitle,
} from '../../components/InformationPage';
import Loader from '../../components/Loader';
import { AlertModal } from '../../components/Modal';
import NotFound from '../../components/NotFound/NotFound';
import Page from '../../components/Page';
import Row from '../../components/Row';
import AuthContext from '../../context/AuthContext/AuthContext';
import ResponsiveContext from '../../context/ResponsiveContext';
import useForm from '../../hooks/useForm';
import { useMutation } from '@apollo/client';
import getDiff from '../../utils/getDiff';
import { EDIT_CHANNEL } from './mutation';
import {
  GET_CHANNEL_INFORMATION,
  GET_TRANSACTION_COUNT,
  GET_GCASH_ONECLICK_DATA,
} from './query';
import { GET_USED_SUBMERCHANT } from '../SystemConfig/query';
import { GET_SUBMERCHANTS } from '../ChannelManagement/query';
import {
  BILL_TYPE_OPTIONS as BILL_TYPES,
  AVAILABLE_GATEWAYS,
  AVAILABLE_PAYMENT_METHODS,
  AVAILABLE_PAYMENT_METHOD_TYPES,
  TOGGLE_OPTIONS,
} from '../../utils/constants';
import { CHANNEL_ERROR_MESSAGES as ERROR_MESSAGES } from '../../utils/errorMessages';

const TransactionFilterContainer = styled(StyledFormField)`
  flex-wrap: wrap;
  width: auto;

  @media (max-width: ${props => props.theme.breakpoint.phone}) {
    & > * {
      margin-bottom: 10px;
    }

    ${PrimaryButton} {
      width: 100%;
    }
  }
`;

const TransactionFilterDateRange = styled(DateRange)`
  font-size: ${props => props.theme.fontSize.xs};
`;

const FormFieldVerificationLink = styled(FormFieldStatic)`
  cursor: pointer;
  text-decoration: underline;
`;

const ActionFields = styled.div`
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;

  width: ${props => {
    const base = 100 / props.perRow;
    const margin = 20;
    return `calc(${base}% - ${margin}px)`;
  }};

  flex: 1;

  margin-left: ${props => (props.isMobile ? '0px' : '20px')};
  margin-bottom: 20px;
`;

const ActionIcon = styled(FontAwesomeIcon)`
  color: ${props => (props.disabled ? 'gray' : props.color)};
  font-size: 20px;
  cursor: pointer;

  margin-left: 10px;
  &:first-child {
    margin-left: 0;
  }
`;

const ActionButton = styled.button`
  background-color: ${props =>
    props.disabled ? 'gray' : props.backgroundColor};
  color: #fff;
  font-size: ${props => props.theme.fontSize.s};
  cursor: pointer;
  flex: 1;
  border-radius: 5px;
`;

const EmailNotificationMode = [
  {
    value: false,
    color: 'red',
    label: 'Disable',
  },
  {
    value: true,
    color: 'green',
    label: 'Enable',
  },
];

const cardHolderOptions = [
  {
    value: false,
    color: 'red',
    label: 'Disable',
  },
  {
    value: true,
    color: 'green',
    label: 'Enable',
  },
];

const cardHolderTypeOptions = [
  {
    value: 'OPTIONAL',
    color: 'green',
    label: 'Optional',
  },
  {
    value: 'REQUIRED',
    color: 'green',
    label: 'Required',
  },
];

const callBackEncryptionOptions = [
  {
    value: false,
    color: 'red',
    label: 'Decrypt',
  },
  {
    value: true,
    color: 'green',
    label: 'Encrypt',
  },
];

const PAYMENT_METHODS = {
  gcash: [
    {
      value: 'mynt',
      label: 'Mynt',
      color: 'green',
    },
  ],
  card: [
    {
      value: 'off',
      label: 'Off',
      color: 'red',
    },
    {
      value: 'adyen',
      label: 'Adyen',
      color: 'green',
    },
    {
      value: 'ipay88',
      label: 'iPay88',
      color: 'green',
    },
  ],
  bank: [
    {
      value: 'off',
      label: 'Off',
      color: 'red',
    },
    {
      value: 'bpi',
      label: 'BPI',
      color: 'green',
    },
  ],
  ewallet: [
    {
      value: 'xendit',
      label: 'Xendit',
      color: 'green',
    },
  ],
};

const psORReceiptingOptions = [
  {
    value: false,
    color: 'red',
    label: 'Disable',
  },
  {
    value: true,
    color: 'green',
    label: 'Enable',
  },
];

const isSecureConnectionOptions = [
  {
    value: false,
    color: 'red',
    label: 'Unsecure',
  },
  {
    value: true,
    color: 'green',
    label: 'Secure',
  },
];

const ChannelInformation = ({ match, history, location }) => {
  const { isMobile } = useContext(ResponsiveContext);
  const { permissions } = useContext(AuthContext);

  const [state, setState] = useState({
    isEditing: false,
    isLeavingPage: false,
    nextLocation: null,

    isConfirmEditChannelModalOpen: false,
    isSuccessEditChannelModalOpen: false,
    isFailureEditChannelModalOpen: false,

    isLeavingWhileEditing: false,

    selectedChannel: null,
    selectedChannelGcashOneClickValues: null,

    transactionFilter: {
      start: addMonths(new Date(), -1),
      end: new Date(),
    },

    loading: true,

    editChannelError: null,

    isShowingAccessKey: false,
    isShowingSecretKey: false,
  });

  const [selectedSubMerchant, setSelectedSubMerchant] = useState({
    serviceType: '',
  });
  const { data: usedSubMerchantsData, loading: usedSubMerchantsLoading } =
    useQuery(GET_USED_SUBMERCHANT, {
      variables: { id: selectedSubMerchant.serviceType },
      fetchPolicy: 'network-only',
    });

  const [newSubmerchants, setNewSubmerchants] = useState([
    {
      merchant: null,
      serviceType: null,
    },
  ]);

  const [submerchantErrors, setSubmerchantErrors] = useState({
    serviceType: {},
    id: {},
  });

  const [newSubmerchantErrors, setNewSubmerchantErrors] = useState([{}]);

  let newValues = newSubmerchants.filter(
    newValue => newValue.serviceType && newValue.merchant
  );

  useEffect(() => {
    if (!usedSubMerchantsLoading && selectedSubMerchant.serviceType) {
      const cantDelete = usedSubMerchantsData.channelSubMerchant;
      if (!cantDelete) {
        let { serviceType, id } = submerchantErrors;
        delete serviceType[`serviceType-${selectedSubMerchant.serviceType}`];
        delete id[`subMerchantId-${selectedSubMerchant.serviceType}`];

        setSubmerchantErrors({ id, serviceType });
        onChange.subMerchants(
          values.subMerchants.filter(submerchant => {
            return submerchant.serviceType !== selectedSubMerchant.serviceType;
          })
        );
      }

      setSelectedSubMerchant({ serviceType: '' });
    }
  }, [usedSubMerchantsData]);

  useEffect(() => {
    if (location.state && location.state.isEditing !== undefined) {
      setState({ ...state, isEditing: location.state.isEditing });
    }
  }, []);

  useEffect(() => {
    const unblock = history.block(location => {
      if (state.isLeavingWhileEditing || !state.isEditing) return true;
      setState({
        ...state,
        nextLocation: location,
        isLeavingWhileEditing: true,
      });
      return false;
    });

    return () => {
      unblock();
    };
  }, [state.isLeavingWhileEditing, state.isEditing]);

  const { data: serviceTypeData } = useQuery(GET_SUBMERCHANTS, {
    variables: { where: { merchant: 'Globe' } },
    fetchPolicy: 'network-only',
  });

  const { data: ecpayServiceTypeData } = useQuery(GET_SUBMERCHANTS, {
    variables: { where: { merchant: 'ECPay' } },
    fetchPolicy: 'network-only',
  });

  const [serviceTypeOptions, setServiceTypeOptions] = useState([]);
  const [ecpayServiceTypeOptions, setEcpayServiceTypeOptions] = useState([]);

  const { data, loading } = useQuery(GET_CHANNEL_INFORMATION, {
    variables: { where: { id: match.params.id } },
    fetchPolicy: 'network-only',
  });

  const { data: gcashOneClickData, loading: gcashOneClickDataLoading } =
    useQuery(GET_GCASH_ONECLICK_DATA, {
      variables: { where: { id: match.params.id } },
      fetchPolicy: 'network-only',
    });

  const { data: countData, loading: isFetchingTransactionCount } = useQuery(
    GET_TRANSACTION_COUNT,
    {
      variables: {
        filter: {
          channelId: match.params.id,
          range: {
            start: startOfDay(state.transactionFilter.start),
            end: startOfDay(addDays(state.transactionFilter.end, 1)),
          },
        },
      },
    }
  );

  const [editChannel, { loading: isEditingChannel }] = useMutation(
    EDIT_CHANNEL,
    {
      onCompleted: () => {
        setState({
          ...state,
          isConfirmEditChannelModalOpen: false,
          isSuccessEditChannelModalOpen: true,
          isEditing: false,
        });
        setNewSubmerchants([
          {
            serviceType: null,
            merchant: null,
          },
        ]);
      },
      onError: err =>
        setState({
          ...state,
          editChannelError: err.networkError.result
            ? err.networkError.result.message
            : null,
          isConfirmEditChannelModalOpen: false,
          isFailureEditChannelModalOpen: true,
        }),
    }
  );

  const { fields, initialValue } = useMemo(() => {
    const fields = {
      name: {
        validation: Yup.string()
          .min(1, 'Minimum should be 1 character')
          .max(32, 'Must not exceed 100 characters')
          .required('Please enter a value')
          .matches(/[^-\s]/, 'Must not be a whitespace')
          .matches(
            /^(?![=,@,+,-])(.+)$/,
            'Input must not begin with this special characters (=,@,+,-)'
          ),
      },
      email: {
        validation: Yup.string()
          .email('Must be an email')
          .required('Please enter a value'),
      },
      // channelId: {
      //   validation: Yup.string()
      //     .min(1, 'Minimum should be 1 character')
      //     .max(100, 'Must not exceed 100 characters')
      //     .required('Please enter a value')
      //     .matches(/[^-\s]/, 'Must not be a whitespace')
      //     .matches(
      //       /^(?![=,@,+,-])(.+)$/,
      //       'Input must not begin with this special characters (=,@,+,-)'
      //     ),
      // },
      channelCode: {
        validation: Yup.string()
          .min(1, 'Minimum should be 1 character')
          .max(5, 'Must not exceed 5 characters')
          .required('Please enter a value')
          .matches(/[^-\s]/, 'Must not be a whitespace')
          .matches(
            /^(?![=,@,+,-])(.+)$/,
            'Input must not begin with this special characters (=,@,+,-)'
          ),
      },
      ipAddress: {
        validation: Yup.string()
          .test('is-ipAddress', 'Invalid format', value => {
            // check if empty, it's ok if empty since it's optional
            if (!value) return true;
            const split = value.split('.');
            if (split.length !== 4) return false;
            for (const i of split) {
              if (String(+i).length !== i.length) return false;
              if (+i < 0 || +i > 255) return false;
            }
            return true;
          })
          .nullable(),
      },
      callbackUrl: {
        validation: () => {
          return data && data.channel && data.channel.isVerified
            ? Yup.string()
                .min(10, 'Minumim of 10')
                .max(2500, 'Maximum of 2500')
                .url('Must be a valid URL')
                .required('Please enter a value')
                .nullable()
            : Yup.string();
        },
      },
      bindCallbackUrl: {
        validation: () => {
          return data && data.channel && data.channel.isVerified
            ? Yup.string()
                .nullable()
                .transform((curr, orig) => (orig === '' ? null : curr))
                .min(10, 'Minimum of 10')
                .max(2500, 'Maximum of 2500')
                .matches(
                  /{\bbindingRequestId\b}/,
                  'Should include {bindingRequestId}'
                )
            : Yup.string();
        },
      },
      apiKey: {
        validation: Yup.string()
          .max(250, 'Must not exceed 250 characters')
          .matches(
            /^$|^(?![=,@,+,-])(.+)$/,
            'Input must not begin with this special characters (=,@,+,-)'
          )
          .nullable(),
      },
      merchantKey: {
        validation: Yup.string()
          .max(250, 'Must not exceed 250 characters')
          .matches(
            /^$|^(?![=,@,+,-])(.+)$/,
            'Input must not begin with this special characters (=,@,+,-)'
          )
          .nullable(),
      },
      merchantCode: {
        validation: Yup.string()
          .max(250, 'Must not exceed 250 characters')
          .matches(
            /^$|^(?![=,@,+,-])(.+)$/,
            'Input must not begin with this special characters (=,@,+,-)'
          )
          .nullable(),
      },
      billType: {
        validation: Yup.string().required('Please select a value').nullable(),
      },
      subMerchants: {},
      gcashPaymentMethod: {},
      ewalletPaymentMethod: {},
      cardPaymentMethod: {},
      bankPaymentMethod: {},
      // Payment gateway fields
      xenditGateway: {
        initialValue: false,
      },
      // Payment method fields
      xenditCardsStraight: {
        initialValue: false,
      },
      xenditCardsInstallment: {
        initialValue: false,
      },
      xenditOtc: {
        initialValue: false,
      },
      // Payment method type fields
      xenditCardsStraightCredit: {
        initialValue: false,
      },
      xenditCardsStraightDebit: {
        initialValue: false,
      },
      xenditCardsInstallmentCredit: {
        initialValue: false,
      },
      xenditOtcEcpay: {
        initialValue: false,
      },
      cardHolderNameType: {},
      enableCardHolderName: {},
      failedEmailNotification: {},
      globeEmailNotification: {},
      globeEmailNotificationPatternId: {
        validation: Yup.string()
          .nullable()
          .max(5, 'Must not exceed 5 digits')
          .matches(/^[0-9]*$/, 'Must be a number only'),
      },
      innoveEmailNotification: {},
      innoveEmailNotificationPatternId: {
        validation: Yup.string()
          .nullable()
          .max(5, 'Must not exceed 5 digits')
          .matches(/^[0-9]*$/, 'Must be a number only'),
      },
      bayanEmailNotification: {},
      bayanEmailNotificationPatternId: {
        validation: Yup.string()
          .nullable()
          .max(5, 'Must not exceed 5 digits')
          .matches(/^[0-9]*$/, 'Must be a number only'),
      },
      gcreditSubMerchantId: {
        validation: Yup.string()
          .nullable()
          .matches(/[^-\s]/, 'Must not be a whitespace')
          .max(25, 'Must not exceed 25 digits')
          .min(1, 'Minimum should be 1 digit')
          .matches(/^[0-9]*$/, 'Must be a number only'),
      },
      callbackPassPhrase: {},
      isCallbackEncrypted: {},
      enablePaymentSession: {},
      isForPayByLink: {},
      isOrEnabled: {},
      notificationSettings: {
        initialValue: [],
      },
      isSecureConnection: {},
    };

    const initialValue = {};
    if (!loading && data && data.channel) {
      for (const name of Object.keys(fields)) {
        if (Object.prototype.hasOwnProperty.call(data.channel, name)) {
          fields[name].initialValue = data.channel[name];
        } else if (name === 'notificationSettings') {
          const notificationSettings = [];
          if (data.channel.smsNotif) notificationSettings.push('SMS');
          if (data.channel.emailNotif) notificationSettings.push('EMAIL');
          fields[name].initialValue = notificationSettings;
        } else if (name === 'xenditGateway') {
          fields[name].initialValue = data.channel.gateways?.xendit
            ? true
            : false;
        } else if (name === 'xenditCardsStraight') {
          fields[name].initialValue = data.channel.gateways?.xendit?.card_straight
            ? true
            : false;
        } else if (name === 'xenditCardsInstallment') {
          fields[name].initialValue = data.channel.gateways?.xendit?.card_installment
            ? true
            : false;
        } else if (name === 'xenditCardsStraightCredit') {
          fields[name].initialValue =
            data.channel.gateways?.xendit?.card_straight?.includes('credit')
              ? true
              : false;
        } else if (name === 'xenditCardsStraightDebit') {
          fields[name].initialValue =
            data.channel.gateways?.xendit?.card_straight?.includes('debit')
              ? true
              : false;
        } else if (name === 'xenditCardsInstallmentCredit') {
          fields[name].initialValue =
            data.channel.gateways?.xendit?.card_installment?.includes('credit')
              ? true
              : false;
        } else if (name === 'xenditOtc') {
          fields[name].initialValue = data.channel.gateways?.xendit?.otc
            ? true
            : false;
        } else if (name === 'xenditOtcEcpay') {
          fields[name].initialValue =
            data.channel.gateways?.xendit?.otc?.includes('ecpay')
              ? true
              : false;
        }
        initialValue[name] = fields[name].initialValue;
      }
    }

    return { fields, initialValue };
  }, [data]);

  const { gcashOneClickFields, gcashOneClickInitialValue } = useMemo(() => {
    const gcashOneClickFields = {
      gcashOneClickEnabled: {},
      gcashOneClickMerchantId: {
        validation: Yup.string().required('Please enter a merchant ID'),
      },
      gcashOneClickClientId: {
        validation: Yup.string().required('Please enter a client ID'),
      },
      gcashOneClickProductCode: {
        validation: Yup.string().required('Please enter a product code'),
      },
      gcashOneClickRedirectUrl: {
        validation: Yup.string().required('Please enter a redirect URL'),
      },
      gcashOneClickBindingIdPrefix: {
        validation: Yup.string().matches(
          /^[a-z]{3}$/,
          'Must be lowercase, 3 characters and not contain special characters or numbers'
        ),
      },
      gcashOneClickValidity: {
        validation: Yup.number().min(1, 'Minimum value should be 1'),
      },
      gcashOneClickClientSecret: {
        validation: Yup.string()
          .required('Please enter a client secret')
          .max(64, 'Maximum value should be 64'),
      },
    };

    const gcashOneClickInitialValue = {};
    if (
      !gcashOneClickDataLoading &&
      gcashOneClickData &&
      gcashOneClickData.channel
    ) {
      for (const name of Object.keys(gcashOneClickFields)) {
        if (
          Object.prototype.hasOwnProperty.call(gcashOneClickData.channel, name)
        ) {
          gcashOneClickFields[name].initialValue =
            gcashOneClickData.channel[name];
        }
        gcashOneClickInitialValue[name] =
          gcashOneClickFields[name].initialValue;
      }
    }

    return {
      gcashOneClickFields,
      gcashOneClickInitialValue,
    };
  }, [gcashOneClickData]);

  const {
    values: gcashOneClickValues,
    onChange: gcashOneClickOnChange,
    onBlur: gcashOneClickOnBlur,
    errors: gcashOneClickErrors,
    onSubmit: gcashOneClickOnSubmit,
    setErrors: gcashOneClickSetErrors,
  } = useForm(gcashOneClickFields);

  /**
   * 1. This effect watches for the value of `gcashOneClickValues.gcashOneClickEnabled`
   * 2. If gcashOneClickEnabled === false, this sets the error values of gcashOneClick useForm to null
   *    to prevent the formfield labels from highlighting to red.
   */

  useEffect(() => {
    if (!gcashOneClickValues.gcashOneClickEnabled) {
      gcashOneClickSetErrors(prevErrors => ({
        ...prevErrors,
        gcashOneClickBindingIdPrefix: null,
        gcashOneClickClientId: null,
        gcashOneClickMerchantId: null,
        gcashOneClickProductCode: null,
        gcashOneClickRedirectUrl: null,
        gcashOneClickClientSecret: null,
        gcashOneClickValidity: null,
      }));
    }
  }, [gcashOneClickValues.gcashOneClickEnabled]);

  const onGCashOneClickSubmit = () => {
    /**
     * 1. If GCash One Click is not enabled, the form validation should be skipped.
     * 2. Form validation is triggered when `onSubmit` from useForm is invoked.
     * 3. Errors are set to null here to prevent the GCash One Click fields from
     *    highlighting to red, causing misleading UX.
     */

    if (!gcashOneClickValues.gcashOneClickEnabled) {
      gcashOneClickSetErrors(prevErrors => ({
        ...prevErrors,
        gcashOneClickBindingIdPrefix: null,
        gcashOneClickClientId: null,
        gcashOneClickMerchantId: null,
        gcashOneClickProductCode: null,
        gcashOneClickRedirectUrl: null,
        gcashOneClickClientSecret: null,
        gcashOneClickValidity: null,
      }));
    } else {
      gcashOneClickOnSubmit();
    }
  };

  const { values, onChange, onBlur, errors, onSubmit } = useForm(
    fields,
    values => {
      setState({
        ...state,
        isConfirmEditChannelModalOpen: true,
        selectedChannel: values,
        selectedChannelGcashOneClickValues: gcashOneClickValues,
      });
    }
  );

  useEffect(() => {
    if (serviceTypeData?.subMerchants && values) {
      let newServiceTypeOptions = serviceTypeData.subMerchants.map(
        subMerchants => ({
          value: subMerchants.serviceType,
          label: subMerchants.serviceType,
        })
      );
      setServiceTypeOptions([
        ...[{ value: null, label: 'None' }],
        ...newServiceTypeOptions,
      ]);
    }
  }, [JSON.stringify(serviceTypeData), JSON.stringify(values)]);

  useEffect(() => {
    if (ecpayServiceTypeData?.subMerchants && values) {
      let newEcpayServiceTypeOptions = ecpayServiceTypeData.subMerchants.map(
        subMerchants => ({
          value: subMerchants.serviceType,
          label: subMerchants.serviceType,
        })
      );

      setEcpayServiceTypeOptions([
        ...[{ value: null, label: 'None' }],
        ...newEcpayServiceTypeOptions,
      ]);
    }
  }, [JSON.stringify(ecpayServiceTypeData), JSON.stringify(values)]);

  function handleNewServiceTypeValidation(event, index) {
    const existsInCurrent = values.subMerchants.find(
      value => value.serviceType === event
    );
    const existsInNew = newSubmerchants.find(
      (value, valIndex) => value.serviceType === event && valIndex !== index
    );

    let newErrors = newSubmerchantErrors;

    if (existsInCurrent) {
      setNewSubmerchantErrors(
        newErrors.map((val, valIndex) => {
          if (valIndex === index) {
            return {
              ...val,
              serviceType: 'Service type already exists',
            };
          }

          return val;
        })
      );

      return;
    }

    if (existsInNew) {
      setNewSubmerchantErrors(
        newErrors.map((val, valIndex) => {
          if (valIndex === index) {
            return {
              ...val,
              serviceType: 'Duplicate service type',
            };
          }

          return val;
        })
      );

      return;
    }

    if (event === null) {
      setNewSubmerchantErrors(
        newErrors.map((val, valIndex) => {
          if (valIndex === index) {
            return {
              ...val,
              serviceType: 'Please select a  service type',
            };
          }

          return val;
        })
      );

      return;
    }

    Yup.string()
      .nullable()
      .validate(event)
      .then(() => {
        if (newSubmerchantErrors[index].serviceType) {
          const submerchantError = newSubmerchantErrors.map((val, valIndex) => {
            if (valIndex === index) {
              delete val.serviceType;
            }

            return val;
          });

          setNewSubmerchantErrors(submerchantError);
        }
      })
      .catch(err => {
        if (err.errors) {
          const submerchantError = newSubmerchantErrors.map((val, valIndex) => {
            if (valIndex === index) {
              return {
                ...val,
                serviceType: err.errors[0],
              };
            }

            return val;
          });

          setNewSubmerchantErrors(submerchantError);
        }
      });
  }

  function handleNewMerchantTypeValidation(event, index) {
    const existsInCurrent = values.subMerchants.find(
      value => value.merchant === event
    );
    const existsInNew = newSubmerchants.find(
      (value, valIndex) => value.merchant === event && valIndex !== index
    );

    let newErrors = newSubmerchantErrors;

    if (existsInCurrent) {
      setNewSubmerchantErrors(
        newErrors.map((val, valIndex) => {
          if (valIndex === index) {
            return {
              ...val,
              merchant: 'Merchant already exists',
            };
          }

          return val;
        })
      );

      return;
    }

    if (existsInNew) {
      setNewSubmerchantErrors(
        newErrors.map((val, valIndex) => {
          if (valIndex === index) {
            return {
              ...val,
              merchant: 'Duplicate Merchant',
            };
          }

          return val;
        })
      );

      return;
    }

    Yup.string()
      .nullable()
      .validate(event)
      .then(() => {
        if (newSubmerchantErrors[index].merchant) {
          const submerchantError = newSubmerchantErrors.map((val, valIndex) => {
            if (valIndex === index) {
              delete val.merchant;
            }

            return val;
          });

          setNewSubmerchantErrors(submerchantError);
        }
      })
      .catch(err => {
        if (err.errors) {
          const submerchantError = newSubmerchantErrors.map((val, valIndex) => {
            if (valIndex === index) {
              return {
                ...val,
                merchant: err.errors[0],
              };
            }

            return val;
          });

          setNewSubmerchantErrors(submerchantError);
        }
      });
  }

  const backButton = (
    <SecondaryButton
      onClick={() => {
        history.push('/channels-management');
      }}
    >
      Back to All Channels
    </SecondaryButton>
  );

  const editButtonGroup = permissions.Role.update &&
    data &&
    data.channel &&
    data.channel.isVerified && (
      <Row>
        {!state.isEditing && (
          <PrimaryButton
            icon="pen"
            onClick={() => setState({ ...state, isEditing: true })}
          >
            Edit
          </PrimaryButton>
        )}
        <PrimaryButton
          icon="save"
          disabled={
            !state.isEditing ||
            Object.keys(submerchantErrors.id).length > 0 ||
            newSubmerchantErrors.find(
              newError => newError.merchant || newError.serviceType
            ) ||
            newSubmerchants.find(
              newSubmerchant =>
                newSubmerchant.serviceType === null && newSubmerchant.merchant
            )
          }
          onClick={() => {
            onSubmit();
            onGCashOneClickSubmit();
          }}
        >
          Save
        </PrimaryButton>
      </Row>
    );

  return (
    <>
      <Page>
        <Header
          path={[
            { label: 'Channel Mgt', to: '/channels-management' },
            data && data.channel ? data.channel.name : '',
          ]}
          title={data && data.channel ? data.channel.name : ''}
          withHome
        />
        <DataContainer loading={loading}>
          {loading && <Loader fullPage />}
          {!loading && !data.channel && <NotFound />}
          {!loading && data.channel && (
            <>
              <DataHeader>
                <DataHeader.Title>
                  {data && data.channel ? data.channel.name : ''}
                </DataHeader.Title>
              </DataHeader>
              <PageSubsection>
                <FormField
                  isStatic
                  value={
                    isFetchingTransactionCount
                      ? 'Loading...'
                      : countData && countData.transactionCount
                        ? countData.transactionCount.count
                        : 0
                  }
                  label="Transaction Count"
                  perRow={3}
                />
                <TransactionFilterContainer
                  row
                  perRow={isMobile ? 1 : 2}
                  horizontalGap={0}
                >
                  <TransactionFilterDateRange
                    name="transaction-filter"
                    handleChange={transactionFilter =>
                      setState({ ...state, transactionFilter })
                    }
                    hasDelimiter
                    value={state.transactionFilter}
                    maxDate={new Date()}
                    minDate={addMonths(new Date(), -6)}
                  />
                  <PrimaryButton>Filter</PrimaryButton>
                </TransactionFilterContainer>
              </PageSubsection>
              <SubsectionTitle>PAYMENT GATEWAYS</SubsectionTitle>
              <PageSubsection>
                <FormField
                  label="Xendit Gateway"
                  name="xenditGateway"
                  type={FIELD_TYPES.TOGGLE}
                  options={TOGGLE_OPTIONS}
                  value={values.xenditGateway}
                  onChange={onChange.xenditGateway}
                  onBlur={onBlur.xenditGateway}
                  error={errors.xenditGateway}
                  readOnly={!state.isEditing}
                  perRow={2}
                />
              </PageSubsection>

              <SubsectionTitle>PAYMENT METHODS</SubsectionTitle>
              <PageSubsection>
                {!values.xenditGateway ? (
                  <FormFieldStatic row={1}>
                    No payment methods available. Please enable a payment
                    gateway first.
                  </FormFieldStatic>
                ) : (
                  <>
                    <FormField
                      label="Cards - Straight Payment Method"
                      name="xenditCardsStraight"
                      type={FIELD_TYPES.TOGGLE}
                      options={TOGGLE_OPTIONS}
                      value={values.xenditCardsStraight}
                      onChange={onChange.xenditCardsStraight}
                      onBlur={onBlur.xenditCardsStraight}
                      error={errors.xenditCardsStraight}
                      readOnly={!state.isEditing}
                      perRow={2}
                    />
                    {values.xenditCardsStraight && (
                      <>
                        <FormField
                          label="Credit Card"
                          name="xenditCardsStraightCredit"
                          type={FIELD_TYPES.CHECKBOX}
                          value={values.xenditCardsStraightCredit}
                          onChange={() =>
                            onChange.xenditCardsStraightCredit(
                              !values.xenditCardsStraightCredit
                            )
                          }
                          onBlur={onBlur.xenditCardsStraightCredit}
                          error={errors.xenditCardsStraightCredit}
                          readOnly={!state.isEditing}
                          noErrors
                          perRow={1}
                        />
                        <FormField
                          label="Debit Card"
                          name="xenditCardsStraightDebit"
                          type={FIELD_TYPES.CHECKBOX}
                          value={values.xenditCardsStraightDebit}
                          onChange={() =>
                            onChange.xenditCardsStraightDebit(!values.xenditCardsStraightDebit)
                          }
                          onBlur={onBlur.xenditCardsStraightDebit}
                          error={errors.xenditCardsStraightDebit}
                          readOnly={!state.isEditing}
                          noErrors
                          perRow={1}
                        />
                      </>
                    )}
                    <FormField
                      label="Cards - Installment Payment Method"
                      name="xenditCardsInstallment"
                      type={FIELD_TYPES.TOGGLE}
                      options={TOGGLE_OPTIONS}
                      value={values.xenditCardsInstallment}
                      onChange={onChange.xenditCardsInstallment}
                      onBlur={onBlur.xenditCardsInstallment}
                      error={errors.xenditCardsInstallment}
                      readOnly={!state.isEditing}
                      perRow={2}
                    />
                    {values.xenditCardsInstallment && (
                      <>
                        <FormField
                          label="Credit Card"
                          name="xenditCardsInstallmentCredit"
                          type={FIELD_TYPES.CHECKBOX}
                          value={values.xenditCardsInstallmentCredit}
                          onChange={() =>
                            onChange.xenditCardsInstallmentCredit(
                              !values.xenditCardsInstallmentCredit
                            )
                          }
                          onBlur={onBlur.xenditCardsInstallmentCredit}
                          error={errors.xenditCardsInstallmentCredit}
                          readOnly={!state.isEditing}
                          noErrors
                          perRow={1}
                        />
                      </>
                    )}
                    <FormField
                      label="Over the Counter Payment Method"
                      name="xenditOtc"
                      type={FIELD_TYPES.TOGGLE}
                      options={TOGGLE_OPTIONS}
                      value={values.xenditOtc}
                      onChange={onChange.xenditOtc}
                      onBlur={onBlur.xenditOtc}
                      error={errors.xenditOtc}
                      readOnly={!state.isEditing}
                      perRow={2}
                    />
                    {values.xenditOtc && (
                      <FormField
                        label="ECPay"
                        name="xenditOtcEcpay"
                        type={FIELD_TYPES.CHECKBOX}
                        value={values.xenditOtcEcpay}
                        onChange={() =>
                          onChange.xenditOtcEcpay(!values.xenditOtcEcpay)
                        }
                        onBlur={onBlur.xenditOtcEcpay}
                        error={errors.xenditOtcEcpay}
                        readOnly={!state.isEditing}
                        noErrors
                        perRow={1}
                      />
                    )}
                  </>
                )}
              </PageSubsection>

              <SubsectionTitle>CHANNEL SETTINGS</SubsectionTitle>
              <PageSubsection>
                <FormField
                  label="Activate Payment Session"
                  name="enablePaymentSession"
                  type={FIELD_TYPES.CHECKBOX}
                  value={values.enablePaymentSession}
                  onChange={onChange.enablePaymentSession}
                  onBlur={onBlur.enablePaymentSession}
                  error={errors.enablePaymentSession}
                  readOnly={!state.isEditing}
                  row
                  noErrors
                  perRow={2}
                />
                <FormField
                  label="Activate Pay-by-Link"
                  name="isForPayByLink"
                  type={FIELD_TYPES.CHECKBOX}
                  value={values.isForPayByLink}
                  onChange={onChange.isForPayByLink}
                  onBlur={onBlur.isForPayByLink}
                  error={errors.isForPayByLink}
                  readOnly={!state.isEditing}
                  row
                  noErrors
                  perRow={2}
                />
              </PageSubsection>

              <SubsectionTitle>CHANNEL SETTINGS</SubsectionTitle>
              <PageSubsection>
                <FormField
                  label="Channel Name"
                  name="name"
                  type={FIELD_TYPES.TEXT}
                  value={values.name}
                  onChange={onChange.name}
                  onBlur={onBlur.name}
                  error={errors.name}
                  readOnly={true}
                  perRow={2}
                  required
                />
                <FormField
                  label="Email"
                  name="email"
                  type={FIELD_TYPES.EMAIL}
                  value={values.email}
                  onChange={onChange.email}
                  onBlur={onBlur.email}
                  error={errors.email}
                  readOnly={true}
                  perRow={2}
                  required
                />
                <FormField
                  label="Channel Code"
                  name="channelCode"
                  type={FIELD_TYPES.TEXT}
                  value={values.channelCode}
                  onChange={onChange.channelCode}
                  onBlur={onBlur.channelCode}
                  error={errors.channelCode}
                  readOnly={!state.isEditing}
                  perRow={2}
                  required
                />
                <FormField
                  label="Bill Type"
                  name="billType"
                  type={FIELD_TYPES.SELECT}
                  options={BILL_TYPES}
                  value={values.billType}
                  onChange={onChange.billType}
                  onBlur={onBlur.billType}
                  error={errors.billType}
                  readOnly={!state.isEditing}
                  perRow={2}
                  required
                />
                {/* <FormField
                  label="Channel ID"
                  name="channelId"
                  type={FIELD_TYPES.TEXT}
                  value={values.channelId}
                  onChange={onChange.channelId}
                  onBlur={onBlur.channelId}
                  error={errors.channelId}
                  readOnly={!state.isEditing}
                  perRow={2}
                  required
                /> */}
                <FormField
                  label="IP Address"
                  name="ipAddress"
                  type={FIELD_TYPES.TEXT}
                  value={values.ipAddress}
                  onChange={onChange.ipAddress}
                  onBlur={onBlur.ipAddress}
                  error={errors.ipAddress}
                  readOnly={!state.isEditing}
                  perRow={2}
                />
                <FormField
                  label="API Key"
                  name="apiKey"
                  type={FIELD_TYPES.TEXT}
                  value={values.apiKey}
                  onChange={onChange.apiKey}
                  onBlur={onBlur.apiKey}
                  error={errors.apiKey}
                  readOnly={!state.isEditing}
                  perRow={2}
                />
                <FormField
                  label="Merchant Code"
                  name="merchantCode"
                  type={FIELD_TYPES.TEXT}
                  value={values.merchantCode}
                  onChange={onChange.merchantCode}
                  onBlur={onBlur.merchantCode}
                  error={errors.merchantCode}
                  readOnly={!state.isEditing}
                  perRow={2}
                />
                <FormField
                  label="Merchant Key"
                  name="merchantKey"
                  type={FIELD_TYPES.TEXT}
                  value={values.merchantKey}
                  onChange={onChange.merchantKey}
                  onBlur={onBlur.merchantKey}
                  error={errors.merchantKey}
                  readOnly={!state.isEditing}
                  perRow={2}
                />
              </PageSubsection>
              {/* <SubsectionTitle>MERCHANT SERVICE TYPE SETTINGS</SubsectionTitle>
              {!loading &&
                values &&
                values.subMerchants &&
                values.subMerchants.map((merchant, index) => (
                  <PageSubsection key={merchant.serviceType + '-' + index}>
                    <FormField
                      placeholder=""
                      label="Merchant"
                      name={`merchant-${merchant.merchant}`}
                      type={FIELD_TYPES.SELECT}
                      value={merchant.merchant}
                      options={[
                        { value: null, label: 'None' },
                        { value: 'Globe', label: 'Globe' },
                        { value: 'ECPay', label: 'ECPay' },
                      ]}
                      onChange={event => {
                        onChange.subMerchants(
                          values.subMerchants.map(submerchant => {
                            if (
                              submerchant.serviceType === merchant.serviceType
                            ) {
                              return {
                                merchant: event,
                                serviceType: null,
                              };
                            }

                            return submerchant;
                          })
                        );
                      }}
                      readOnly={!state.isEditing}
                      perRow={2.5}
                      required
                    />
                    <FormField
                      placeholder="Service Type"
                      label="Service Type"
                      name={`merchant-${merchant.serviceType + '-' + index}`}
                      type={FIELD_TYPES.SELECT}
                      value={merchant.serviceType}
                      options={
                        merchant.merchant === 'ECPay'
                          ? ecpayServiceTypeOptions
                          : serviceTypeOptions
                      }
                      onChange={event => {
                        onChange.subMerchants(
                          values.subMerchants.map(submerchant => {
                            if (
                              submerchant.serviceType === merchant.serviceType
                            ) {
                              return {
                                merchant: merchant.merchant,
                                serviceType: event,
                              };
                            }

                            return submerchant;
                          })
                        );
                      }}
                      readOnly={!state.isEditing}
                      perRow={2.5}
                      required
                    />
                    {state.isEditing ? (
                      <ActionFields
                        isMobile={isMobile}
                        fieldsPerRow={2.5}
                        perRow={5}
                      >
                        {isMobile ? (
                          <ActionButton
                            backgroundColor={
                              usedSubMerchantsLoading ? 'gray' : 'red'
                            }
                            onClick={() => {
                              if (!usedSubMerchantsLoading) {
                                setSelectedSubMerchant(merchant);
                              }
                            }}
                          >
                            Delete
                          </ActionButton>
                        ) : (
                          <ActionIcon
                            icon={
                              usedSubMerchantsLoading
                                ? 'spinner'
                                : 'minus-circle'
                            }
                            color={usedSubMerchantsLoading ? 'gray' : 'red'}
                            onClick={() => {
                              if (!usedSubMerchantsLoading) {
                                setSelectedSubMerchant(merchant);
                              }
                            }}
                          />
                        )}
                      </ActionFields>
                    ) : (
                      ''
                    )}
                  </PageSubsection>
                ))}
              {!state.isEditing &&
                newSubmerchants.map((newSubmerchant, index) => {
                  return (
                    <PageSubsection key={index}>
                      <FormField
                        placeholder=""
                        label="Merchant"
                        name={`newServiceType-${index}`}
                        type={FIELD_TYPES.SELECT}
                        value={newSubmerchant.merchant}
                        onChange={event => {
                          let newValue = newSubmerchants.map(
                            (newValue, newIndex) => {
                              if (newIndex === index) {
                                return {
                                  merchant: event,
                                  serviceType: null,
                                };
                              }

                              return newValue;
                            }
                          );

                          setNewSubmerchants(newValue);
                          handleNewMerchantTypeValidation(event, index);
                        }}
                        options={[
                          { value: null, label: 'None' },
                          { value: 'Globe', label: 'Globe' },
                          { value: 'ECPay', label: 'ECPay' },
                        ]}
                        error={newSubmerchantErrors[index].merchant}
                        onBlur={event => {
                          handleNewMerchantTypeValidation(event, index);
                        }}
                        readOnly={!state.isEditing}
                        perRow={2.5}
                        required
                      />
                      <FormField
                        placeholder="Service Type"
                        label="Service Type"
                        name={`newServiceType-${index}`}
                        type={FIELD_TYPES.SELECT}
                        value={newSubmerchant.serviceType}
                        options={
                          newSubmerchant.merchant === 'ECPay'
                            ? ecpayServiceTypeOptions
                            : serviceTypeOptions
                        }
                        onChange={event => {
                          let newValue = newSubmerchants.map(
                            (newValue, newIndex) => {
                              if (newIndex === index) {
                                return {
                                  merchant: newSubmerchant.merchant,
                                  serviceType: event,
                                };
                              }

                              return newValue;
                            }
                          );

                          setNewSubmerchants(newValue);
                          handleNewServiceTypeValidation(event, index);
                        }}
                        onBlur={event => {
                          handleNewServiceTypeValidation(event);
                        }}
                        readOnly={!state.isEditing}
                        error={newSubmerchantErrors[index].serviceType}
                        perRow={2.5}
                        required
                      />
                      <ActionFields
                        isMobile={isMobile}
                        fieldsPerRow={2.5}
                        perRow={5}
                      ></ActionFields>
                    </PageSubsection>
                  );
                })}
              {state.isEditing &&
                newSubmerchants.map((newSubmerchant, index) => {
                  return (
                    <PageSubsection key={index}>
                      <FormField
                        placeholder=""
                        label="Merchant"
                        name={`newServiceType-${index}`}
                        type={FIELD_TYPES.SELECT}
                        value={newSubmerchant.merchant}
                        onChange={event => {
                          let newValue = newSubmerchants.map(
                            (newValue, newIndex) => {
                              if (newIndex === index) {
                                return {
                                  merchant: event,
                                  serviceType: null,
                                };
                              }

                              return newValue;
                            }
                          );

                          setNewSubmerchants(newValue);
                          handleNewMerchantTypeValidation(event, index);
                        }}
                        options={[
                          { value: null, label: 'None' },
                          { value: 'Globe', label: 'Globe' },
                          { value: 'ECPay', label: 'ECPay' },
                        ]}
                        error={newSubmerchantErrors[index].merchant}
                        onBlur={event => {
                          handleNewMerchantTypeValidation(event, index);
                        }}
                        perRow={2.5}
                        required
                      />
                      <FormField
                        placeholder="Service Type"
                        label="Service Type"
                        name={`newServiceType-${index}`}
                        type={FIELD_TYPES.SELECT}
                        value={newSubmerchant.serviceType}
                        options={
                          newSubmerchant.merchant === 'ECPay'
                            ? ecpayServiceTypeOptions
                            : serviceTypeOptions
                        }
                        onChange={event => {
                          let newValue = newSubmerchants.map(
                            (newValue, newIndex) => {
                              if (newIndex === index) {
                                return {
                                  merchant: newSubmerchant.merchant,
                                  serviceType: event,
                                };
                              }

                              return newValue;
                            }
                          );

                          setNewSubmerchants(newValue);
                          handleNewServiceTypeValidation(event, index);
                        }}
                        onBlur={event => {
                          handleNewServiceTypeValidation(event);
                        }}
                        readOnly={newSubmerchant.merchant === null}
                        error={newSubmerchantErrors[index].serviceType}
                        perRow={2.5}
                        required
                      />
                      <ActionFields
                        isMobile={isMobile}
                        fieldsPerRow={2.5}
                        perRow={5}
                      >
                        {!(
                          newSubmerchantErrors[index].serviceType ||
                          newSubmerchantErrors[index].merchant
                        ) &&
                          newSubmerchant.serviceType &&
                          newSubmerchant.merchant &&
                          index === newSubmerchants.length - 1 &&
                          (isMobile ? (
                            <ActionButton
                              backgroundColor="green"
                              onClick={() => {
                                setNewSubmerchants([
                                  ...newSubmerchants,
                                  {
                                    serviceType: null,
                                    merchant: null,
                                  },
                                ]);
                                setNewSubmerchantErrors([
                                  ...newSubmerchantErrors,
                                  {},
                                ]);
                              }}
                            >
                              Add
                            </ActionButton>
                          ) : (
                            <ActionIcon
                              icon="plus-circle"
                              color="green"
                              onClick={() => {
                                setNewSubmerchants([
                                  ...newSubmerchants,
                                  {
                                    serviceType: null,
                                    merchant: null,
                                  },
                                ]);
                                setNewSubmerchantErrors([
                                  ...newSubmerchantErrors,
                                  {},
                                ]);
                              }}
                            />
                          ))}
                        {isMobile ? (
                          <ActionButton
                            backgroundColor="red"
                            onClick={() => {
                              let newValue = newSubmerchants.filter(
                                (submerchant, submerchantIndex) => {
                                  return submerchantIndex !== index;
                                }
                              );

                              let newErrors = newSubmerchantErrors.filter(
                                (submerchant, submerchantIndex) => {
                                  return submerchantIndex !== index;
                                }
                              );

                              if (newValue.length === 0) {
                                newValue.push({
                                  serviceType: null,
                                  merchant: null,
                                });
                                newErrors.push({});
                              }

                              setNewSubmerchants(newValue);
                              setNewSubmerchantErrors(newErrors);
                            }}
                          >
                            Delete
                          </ActionButton>
                        ) : (
                          <ActionIcon
                            icon="minus-circle"
                            color="red"
                            onClick={() => {
                              let newValue = newSubmerchants.filter(
                                (submerchant, submerchantIndex) => {
                                  return submerchantIndex !== index;
                                }
                              );

                              let newErrors = newSubmerchantErrors.filter(
                                (submerchant, submerchantIndex) => {
                                  return submerchantIndex !== index;
                                }
                              );

                              if (newValue.length === 0) {
                                newValue.push({
                                  serviceType: null,
                                  merchant: null,
                                });
                                newErrors.push({});
                              }

                              setNewSubmerchants(newValue);
                              setNewSubmerchantErrors(newErrors);
                            }}
                          />
                        )}
                      </ActionFields>
                    </PageSubsection>
                  );
                })} */}
              {/* <SubsectionTitle>CARD HOLDER SETTINGS</SubsectionTitle>
              <PageSubsection>
                <FormField
                  label="Card Holder Name Display"
                  name="enableCardHolderName"
                  type={FIELD_TYPES.TOGGLE}
                  options={cardHolderOptions}
                  value={
                    values.enableCardHolderName === null
                      ? false
                      : values.enableCardHolderName
                  }
                  onChange={onChange.enableCardHolderName}
                  onBlur={onBlur.enableCardHolderName}
                  error={errors.enableCardHolderName}
                  readOnly={!state.isEditing}
                  perRow={2}
                />
                {values.enableCardHolderName ? (
                  <FormField
                    label="Card Holder Name Type"
                    name="cardHolderNameType"
                    type={FIELD_TYPES.TOGGLE}
                    options={cardHolderTypeOptions}
                    value={
                      values.cardHolderNameType === null
                        ? 'OPTIONAL'
                        : values.cardHolderNameType
                    }
                    onChange={onChange.cardHolderNameType}
                    onBlur={onBlur.cardHolderNameType}
                    error={errors.cardHolderNameType}
                    readOnly={!state.isEditing}
                    perRow={2}
                  />
                ) : (
                  ''
                )}
              </PageSubsection> */}
              <SubsectionTitle>PAYMENT METHODS</SubsectionTitle>
              <PageSubsection>
                <FormField
                  label="GCash"
                  name="gcashPaymentMethod"
                  type={FIELD_TYPES.TOGGLE}
                  value={
                    values.gcashPaymentMethod === null
                      ? 'mynt'
                      : values.gcashPaymentMethod
                  }
                  onChange={onChange.gcashPaymentMethod}
                  onBlur={onBlur.gcashPaymentMethod}
                  error={errors.gcashPaymentMethod}
                  options={PAYMENT_METHODS.gcash}
                  readOnly={!state.isEditing}
                  perRow={2}
                  required
                />
                <FormField
                  label="GCredit Sub-Mid"
                  name="gcreditSubMerchantId"
                  type={FIELD_TYPES.TEXT}
                  value={values.gcreditSubMerchantId}
                  onChange={onChange.gcreditSubMerchantId}
                  onBlur={onBlur.gcreditSubMerchantId}
                  error={errors.gcreditSubMerchantId}
                  readOnly={!state.isEditing}
                  perRow={2}
                />
                {/* <FormField
                  label="Card"
                  name="cardPaymentMethod"
                  type={FIELD_TYPES.TOGGLE}
                  value={
                    values.cardPaymentMethod === null
                      ? 'off'
                      : values.cardPaymentMethod
                  }
                  onChange={onChange.cardPaymentMethod}
                  onBlur={onBlur.cardPaymentMethod}
                  error={errors.cardPaymentMethod}
                  options={PAYMENT_METHODS.card}
                  readOnly={!state.isEditing}
                  perRow={2}
                  required
                />
                <FormField
                  label="Bank"
                  name="bankPaymentMethod"
                  type={FIELD_TYPES.TOGGLE}
                  value={values.bankPaymentMethod || 'off'}
                  onChange={onChange.bankPaymentMethod}
                  onBlur={onBlur.bankPaymentMethod}
                  error={errors.bankPaymentMethod}
                  options={PAYMENT_METHODS.bank}
                  readOnly={!state.isEditing}
                  perRow={2}
                  required
                /> */}
                <FormField
                  label="Xendit"
                  name="ewalletPaymentMethod"
                  type={FIELD_TYPES.TOGGLE}
                  value={
                    values.ewalletPaymentMethod === null
                      ? 'xendit'
                      : values.ewalletPaymentMethod
                  }
                  onChange={onChange.ewalletPaymentMethod}
                  onBlur={onBlur.ewalletPaymentMethod}
                  error={errors.ewalletPaymentMethod}
                  options={PAYMENT_METHODS.ewallet}
                  readOnly={!state.isEditing}
                  perRow={2}
                  required
                />
              </PageSubsection>
              {/* <SubsectionTitle>OR RECEIPTING SETTINGS</SubsectionTitle>
              <PageSubsection>
                <FormField
                  label="OR Receipting"
                  name="isOrEnabled"
                  type={FIELD_TYPES.TOGGLE}
                  value={values.isOrEnabled || false}
                  onChange={onChange.isOrEnabled}
                  onBlur={onBlur.isOrEnabled}
                  error={errors.isOrEnabled}
                  options={psORReceiptingOptions}
                  readOnly={!state.isEditing}
                  perRow={2}
                  required
                />
              </PageSubsection> */}
              {/* <SubsectionTitle>EMAIL NOTIFICATION</SubsectionTitle>
              <PageSubsection>
                <FormField
                  label="Globe Email Notification Pattern Id"
                  name="globeEmailNotificationPatternId"
                  type={FIELD_TYPES.TEXT}
                  value={values.globeEmailNotificationPatternId}
                  onChange={onChange.globeEmailNotificationPatternId}
                  onBlur={onBlur.globeEmailNotificationPatternId}
                  error={errors.globeEmailNotificationPatternId}
                  readOnly={!state.isEditing}
                  perRow={2}
                />
                <FormField
                  label="Globe Email Notification Status"
                  name="globeEmailNotification"
                  type={FIELD_TYPES.TOGGLE}
                  value={
                    !!values.globeEmailNotification ||
                    values.globeEmailNotification === null
                  }
                  options={EmailNotificationMode}
                  onChange={onChange.globeEmailNotification}
                  onBlur={onBlur.globeEmailNotification}
                  error={errors.globeEmailNotification}
                  readOnly={!state.isEditing}
                  perRow={2}
                />
                <FormField
                  label="Innove Email Notification Pattern Id"
                  name="innoveEmailNotificationPatternId"
                  type={FIELD_TYPES.TEXT}
                  value={values.innoveEmailNotificationPatternId}
                  onChange={onChange.innoveEmailNotificationPatternId}
                  onBlur={onBlur.innoveEmailNotificationPatternId}
                  error={errors.innoveEmailNotificationPatternId}
                  readOnly={!state.isEditing}
                  perRow={2}
                />
                <FormField
                  label="Innove Email Notification Status"
                  name="innoveEmailNotification"
                  type={FIELD_TYPES.TOGGLE}
                  value={
                    !!values.innoveEmailNotification ||
                    values.innoveEmailNotification === null
                  }
                  options={EmailNotificationMode}
                  onChange={onChange.innoveEmailNotification}
                  onBlur={onBlur.innoveEmailNotification}
                  error={errors.innoveEmailNotification}
                  readOnly={!state.isEditing}
                  perRow={2}
                />
                <FormField
                  label="Bayan Email Notification Pattern Id"
                  name="bayanEmailNotificationPatternId"
                  type={FIELD_TYPES.TEXT}
                  value={values.bayanEmailNotificationPatternId}
                  onChange={onChange.bayanEmailNotificationPatternId}
                  onBlur={onBlur.bayanEmailNotificationPatternId}
                  error={errors.bayanEmailNotificationPatternId}
                  readOnly={!state.isEditing}
                  perRow={2}
                />
                <FormField
                  label="Bayan Email Notification Status"
                  name="bayanEmailNotification"
                  type={FIELD_TYPES.TOGGLE}
                  value={
                    !!values.bayanEmailNotification ||
                    values.bayanEmailNotification === null
                  }
                  options={EmailNotificationMode}
                  onChange={onChange.bayanEmailNotification}
                  onBlur={onBlur.bayanEmailNotification}
                  error={errors.bayanEmailNotification}
                  readOnly={!state.isEditing}
                  perRow={2}
                />
                <FormField
                  label="Callback Encryption"
                  name="isCallbackEncrypted"
                  type={FIELD_TYPES.TOGGLE}
                  options={callBackEncryptionOptions}
                  value={
                    values.isCallbackEncrypted === null
                      ? false
                      : values.isCallbackEncrypted
                  }
                  onChange={onChange.isCallbackEncrypted}
                  onBlur={onBlur.isCallbackEncrypted}
                  error={errors.isCallbackEncrypted}
                  readOnly={!state.isEditing}
                  perRow={2}
                />
                <FormField
                  label="Password"
                  isStatic
                  value={
                    data.channel.isCallbackEncrypted ? (
                      values.callbackPassPhrase ? (
                        <Row>
                          <span style={{ fontFamily: 'monospace' }}>
                            {'*'.repeat(values.callbackPassPhrase.length)}{' '}
                          </span>
                        </Row>
                      ) : (
                        'No Password Required'
                      )
                    ) : (
                      'No Password Required'
                    )
                  }
                  perRow={2}
                />
                <FormField
                  label="Failed Email Notification Status"
                  name="failedEmailNotification"
                  type={FIELD_TYPES.TOGGLE}
                  value={
                    !!values.failedEmailNotification ||
                    values.failedEmailNotification === null
                  }
                  options={EmailNotificationMode}
                  onChange={onChange.failedEmailNotification}
                  onBlur={onBlur.failedEmailNotification}
                  error={errors.failedEmailNotification}
                  readOnly={!state.isEditing}
                  perRow={2}
                />
              </PageSubsection> */}
              <SubsectionTitle>PAYMENT NOTIFICATION</SubsectionTitle>
              <PageSubsection>
                <FormField
                  label="Client ID"
                  isStatic
                  value={
                    data.channel.isVerified ? (
                      <Row>
                        <span style={{ fontFamily: 'monospace' }}>
                          {state.isShowingAccessKey
                            ? data.channel.clientId
                            : '*'.repeat(data.channel.clientId.length)}{' '}
                        </span>
                        <FontAwesomeIcon
                          icon={state.isShowingAccessKey ? 'eye' : 'eye-slash'}
                          style={{ cursor: 'pointer', marginLeft: 10 }}
                          onClick={() =>
                            setState({
                              ...state,
                              isShowingAccessKey: !state.isShowingAccessKey,
                            })
                          }
                          color="#7D7D7D"
                        />
                      </Row>
                    ) : (
                      'Not Verified'
                    )
                  }
                  perRow={2}
                />
                <FormField
                  label="Client Secret"
                  isStatic
                  value={
                    'Refer to the JSON file downloaded from Channel Creation.'
                    // data.channel.isVerified ? (
                    //   <Row>
                    //     <span style={{ fontFamily: 'monospace' }}>
                    //       {state.isShowingSecretKey
                    //         ? data.channel.clientSecret
                    //         : '*'.repeat(data.channel.clientSecret.length)}{' '}
                    //     </span>
                    //     <FontAwesomeIcon
                    //       icon={state.isShowingSecretKey ? 'eye' : 'eye-slash'}
                    //       style={{ cursor: 'pointer', marginLeft: 10 }}
                    //       onClick={() =>
                    //         setState({
                    //           ...state,
                    //           isShowingSecretKey: !state.isShowingSecretKey,
                    //         })
                    //       }
                    //       color="#7D7D7D"
                    //     />
                    //   </Row>
                    // ) : (
                    //   'Not Verified'
                    // )
                  }
                  perRow={2}
                />
                {data.channel.isVerified && (
                  <FormField
                    label="Callback URL"
                    name="callbackUrl"
                    type={FIELD_TYPES.TEXT}
                    value={values.callbackUrl}
                    onChange={onChange.callbackUrl}
                    onBlur={onBlur.callbackUrl}
                    error={errors.callbackUrl}
                    readOnly={!state.isEditing}
                    perRow={2}
                    required
                  />
                )}
                {data.channel.isVerified && (
                  <FormField
                    label="Bind Callback URL"
                    name="bindCallbackUrl"
                    type={FIELD_TYPES.TEXT}
                    value={values.bindCallbackUrl}
                    onChange={onChange.bindCallbackUrl}
                    onBlur={onBlur.bindCallbackUrl}
                    error={errors.bindCallbackUrl}
                    readOnly={!state.isEditing}
                    perRow={2}
                  />
                )}
              </PageSubsection>
              {data.channel.isVerified && (
                <PageSubsection>
                  <p
                    style={{
                      fontSize: 12,
                      marginBottom: 30,
                      color: '#777777',
                      fontStyle: 'italic',
                    }}
                  >
                    The Access Key and Secret Key are already URL encoded.
                  </p>
                </PageSubsection>
              )}
              <SubsectionTitle>SECURE CONNECTION SETTINGS</SubsectionTitle>
              <PageSubsection>
                <FormField
                  label="Secure Connection"
                  name="isSecureConnection"
                  type={FIELD_TYPES.TOGGLE}
                  value={
                    values.isSecureConnection === null
                      ? true
                      : values.isSecureConnection
                  }
                  onChange={onChange.isSecureConnection}
                  onBlur={onBlur.isSecureConnection}
                  error={errors.isSecureConnection}
                  options={isSecureConnectionOptions}
                  readOnly={!state.isEditing}
                  perRow={2}
                />
              </PageSubsection>
              {/* <SubsectionTitle>GCASH ONE-CLICK</SubsectionTitle>
              <PageSubsection>
                <FormField
                  label="Enable GCash One Click"
                  name="gcashOneClickEnabled"
                  type={FIELD_TYPES.CHECKBOX}
                  value={gcashOneClickValues.gcashOneClickEnabled}
                  onChange={gcashOneClickOnChange.gcashOneClickEnabled}
                  onBlur={gcashOneClickOnBlur.gcashOneClickEnabled}
                  error={gcashOneClickErrors.gcashOneClickEnabled}
                  readOnly={!state.isEditing}
                  row
                  noErrors
                  perRow={2}
                />
                <FormField
                  label="Merchant ID"
                  name="gcashOneClickMerchantId"
                  type={FIELD_TYPES.TEXT}
                  value={gcashOneClickValues.gcashOneClickMerchantId}
                  onChange={gcashOneClickOnChange.gcashOneClickMerchantId}
                  onBlur={gcashOneClickOnBlur.gcashOneClickMerchantId}
                  error={gcashOneClickErrors.gcashOneClickMerchantId}
                  readOnly={
                    !state.isEditing ||
                    !gcashOneClickValues.gcashOneClickEnabled
                  }
                  perRow={2}
                  required
                />
                <FormField
                  label="Redirect URL"
                  name="gcashOneClickRedirectUrl"
                  type={FIELD_TYPES.TEXT}
                  value={gcashOneClickValues.gcashOneClickRedirectUrl}
                  onChange={gcashOneClickOnChange.gcashOneClickRedirectUrl}
                  onBlur={gcashOneClickOnBlur.gcashOneClickRedirectUrl}
                  error={gcashOneClickErrors.gcashOneClickRedirectUrl}
                  readOnly={
                    !state.isEditing ||
                    !gcashOneClickValues.gcashOneClickEnabled
                  }
                  perRow={2}
                  required
                />
                <FormField
                  label="Client ID"
                  name="gcashOneClickClientId"
                  type={FIELD_TYPES.TEXT}
                  value={gcashOneClickValues.gcashOneClickClientId}
                  onChange={gcashOneClickOnChange.gcashOneClickClientId}
                  onBlur={gcashOneClickOnBlur.gcashOneClickClientId}
                  error={gcashOneClickErrors.gcashOneClickClientId}
                  readOnly={
                    !state.isEditing ||
                    !gcashOneClickValues.gcashOneClickEnabled
                  }
                  perRow={2}
                  required
                />
                <FormField
                  label="Validity (in years)"
                  name="gcashOneClickValidity"
                  type={FIELD_TYPES.NUMBER}
                  value={gcashOneClickValues.gcashOneClickValidity}
                  onChange={gcashOneClickOnChange.gcashOneClickValidity}
                  onBlur={gcashOneClickOnBlur.gcashOneClickValidity}
                  error={gcashOneClickErrors.gcashOneClickValidity}
                  readOnly={
                    !state.isEditing ||
                    !gcashOneClickValues.gcashOneClickEnabled
                  }
                  perRow={2}
                  required
                />
                <FormField
                  label="Product Code"
                  name="gcashOneClickProductCode"
                  type={FIELD_TYPES.TEXT}
                  value={gcashOneClickValues.gcashOneClickProductCode}
                  onChange={gcashOneClickOnChange.gcashOneClickProductCode}
                  onBlur={gcashOneClickOnBlur.gcashOneClickProductCode}
                  error={gcashOneClickErrors.gcashOneClickProductCode}
                  readOnly={
                    !state.isEditing ||
                    !gcashOneClickValues.gcashOneClickEnabled
                  }
                  perRow={2}
                  required
                />
                <FormField
                  label="Binding ID Prefix"
                  name="gcashOneClickBindingIdPrefix"
                  type={FIELD_TYPES.TEXT}
                  value={gcashOneClickValues.gcashOneClickBindingIdPrefix}
                  onChange={gcashOneClickOnChange.gcashOneClickBindingIdPrefix}
                  onBlur={gcashOneClickOnBlur.gcashOneClickBindingIdPrefix}
                  error={gcashOneClickErrors.gcashOneClickBindingIdPrefix}
                  readOnly={
                    !state.isEditing ||
                    !gcashOneClickValues.gcashOneClickEnabled
                  }
                  perRow={2}
                  required
                />
                <FormField
                  label="GCash Client Secret"
                  name="gcashOneClickClientSecret"
                  type={FIELD_TYPES.PASSWORD_TOGGLE}
                  value={gcashOneClickValues.gcashOneClickClientSecret || ''}
                  onChange={gcashOneClickOnChange.gcashOneClickClientSecret}
                  onBlur={gcashOneClickOnBlur.gcashOneClickClientSecret}
                  error={gcashOneClickErrors.gcashOneClickClientSecret}
                  readOnly={
                    !state.isEditing ||
                    !gcashOneClickValues.gcashOneClickEnabled
                  }
                  perRow={2}
                  required
                />
              </PageSubsection> */}
              {!data.channel.isVerified && (
                <>
                  <SubsectionTitle>VERIFY CHANNEL</SubsectionTitle>
                  <PageSubsection>
                    <StyledFormField
                      perRow={1}
                      horizontalGap={isMobile ? 0 : 40}
                      verticalGap={20}
                    >
                      <FormFieldLabel>Verification Link</FormFieldLabel>
                      <FormFieldVerificationLink
                        onClick={() => {
                          window.open(
                            window.location.origin +
                              `?to=/verify/${data.channel.email}?channelId=${data.channel.id}`,
                            '_blank'
                          );
                        }}
                      >
                        {window.location.origin +
                          `?to=/verify/${data.channel.email}?channelId=${data.channel.id}`}
                      </FormFieldVerificationLink>
                    </StyledFormField>
                  </PageSubsection>
                </>
              )}
              {/* <FixedWidthRow>
                <FixWidthRowPart>
                  <SubsectionTitle>USER ROLES</SubsectionTitle>
                  <HalfPageSubsection>
                    <StyledFormField
                      perRow={1}
                      horizontalGap={0}
                      verticalGap={20}
                    >
                      <FormFieldLabel>Allowed</FormFieldLabel>
                      <FormFieldStatic>None</FormFieldStatic>
                    </StyledFormField>
                    <StyledFormField
                      perRow={1}
                      horizontalGap={0}
                      verticalGap={20}
                    >
                      <FormFieldLabel>Not Allowed</FormFieldLabel>
                      <FormFieldStatic>None</FormFieldStatic>
                    </StyledFormField>
                  </HalfPageSubsection>
                </FixWidthRowPart>
                <FixWidthRowPart>
                  <SubsectionTitle>PAYMENT METHODS</SubsectionTitle>
                  <HalfPageSubsection>
                    <StyledFormField
                      perRow={1}
                      horizontalGap={0}
                      verticalGap={20}
                    >
                      <FormFieldLabel>Allowed</FormFieldLabel>
                      <FormFieldStatic>None</FormFieldStatic>
                    </StyledFormField>
                    <StyledFormField
                      perRow={1}
                      horizontalGap={0}
                      verticalGap={20}
                    >
                      <FormFieldLabel>Not Allowed</FormFieldLabel>
                      <FormFieldStatic>None</FormFieldStatic>
                    </StyledFormField>
                  </HalfPageSubsection>
                </FixWidthRowPart>
              </FixedWidthRow>
              <SubsectionTitle>NOTIFICATION SETTINGS</SubsectionTitle>
              <PageSubsection>
                <FormField
                  name="notificationSettings"
                  type={FIELD_TYPES.CHECKBOX_GROUP}
                  value={values.notificationSettings}
                  onChange={onChange.notificationSettings}
                  onBlur={onBlur.notificationSettings}
                  error={errors.notificationSettings}
                  readOnly={!state.isEditing}
                  options={[{ value: 'EMAIL', label: 'EMAIL' }]}
                />
              </PageSubsection> */}
              <PageSubsection>
                <ButtonsContainer>
                  {isMobile ? (
                    <>
                      {editButtonGroup}
                      {backButton}
                    </>
                  ) : (
                    <>
                      {backButton}
                      {editButtonGroup}
                    </>
                  )}
                </ButtonsContainer>
              </PageSubsection>
            </>
          )}
        </DataContainer>
      </Page>
      <AlertModal
        isOpen={state.isConfirmEditChannelModalOpen}
        title="Save Changes Alert"
        variant="warn"
        icon="exclamation-circle"
        header="ARE YOU SURE?"
        subHeader="You are about to save changes on the Channel."
        description={[
          'This action requires your confirmation.',
          'Action cannot be undone',
        ]}
        confirmText="Yes"
        confirmLoading={isEditingChannel}
        handleConfirm={() => {
          const value = getDiff(initialValue, state.selectedChannel);
          const gcashOneClickValueDiff = getDiff(
            gcashOneClickInitialValue,
            state.selectedChannelGcashOneClickValues
          );

          if (value.notificationSettings) {
            const { notificationSettings } = value;
            value.smsNotif = notificationSettings.includes('SMS');
            value.emailNotif = notificationSettings.includes('EMAIL');
            delete value.notificationSettings;
          }

          const hasGCashOneClickErrors =
            state.selectedChannelGcashOneClickValues.gcashOneClickEnabled &&
            Object.values(gcashOneClickErrors).some(value => value !== null);

          if (hasGCashOneClickErrors) {
            return;
          }

          // Prepare payment gateway data
          const gatewaysData = {};

          // Only add Xendit gateway if it's enabled
          if (values.xenditGateway) {
            gatewaysData.xendit = {};

            // Add cards payment method if enabled
            if (values.xenditCardsStraight) {
              const cardStraightTypes = [];
              if (values.xenditCardsStraightCredit) cardStraightTypes.push('credit');
              if (values.xenditCardsStraightDebit) cardStraightTypes.push('debit');

              if (cardStraightTypes.length > 0) {
                gatewaysData.xendit.card_straight = cardStraightTypes;
              }
            }
            if (values.xenditCardsInstallment) {
              const cardInstallmentTypes = [];

              if (values.xenditCardsInstallmentCredit) cardInstallmentTypes.push('credit');
            

              if (cardInstallmentTypes.length > 0) {
                gatewaysData.xendit.card_installment = cardInstallmentTypes;
              }
            }

            // Add OTC payment method if enabled
            if (values.xenditOtc) {
              const otcTypes = [];
              if (values.xenditOtcEcpay) otcTypes.push('ecpay');

              if (otcTypes.length > 0) {
                gatewaysData.xendit.otc = otcTypes;
              }
            }
          }

          // delete gateway form values since they are transformed using gatewaysData
          delete value.xenditGateway;
          delete value.xenditCardsStraight;
          delete value.xenditCardsInstallment;
          delete value.xenditCardsStraightCredit;
          delete value.xenditCardsStraightDebit;
          delete value.xenditCardsInstallmentCredit;
          delete value.xenditOtc;
          delete value.xenditOtcEcpay;

          editChannel({
            variables: {
              data: {
                ...value,
                ...gcashOneClickValueDiff,
                ...(gcashOneClickValueDiff.gcashOneClickValidity && {
                  gcashOneClickValidity: parseInt(
                    gcashOneClickValueDiff.gcashOneClickValidity
                  ),
                }),
                subMerchants: [
                  ...values.subMerchants.map(currentVal => {
                    return {
                      merchant: currentVal.merchant,
                      serviceType: currentVal.serviceType,
                    };
                  }),
                  ...newValues,
                ],
                // Add gateways data if there are any enabled gateways
                ...(Object.keys(gatewaysData).length > 0 && {
                  gateways: gatewaysData,
                }),
              },
              where: { id: data.channel.id },
            },
          });
        }}
        handleClose={() =>
          setState({ ...state, isConfirmEditChannelModalOpen: false })
        }
      />
      <AlertModal
        isOpen={state.isSuccessEditChannelModalOpen}
        title="Save Changes Alert"
        variant="success"
        icon="check-circle"
        header="SUCCESS!"
        subHeader="Changes have been saved successfully."
        description="Notification will be sent on the Channels's email address regarding changes on the channel."
        confirmText="Go to All Channels"
        handleConfirm={() => {
          history.push('/channels-management');
        }}
        handleClose={() =>
          setState({ ...state, isSuccessEditChannelModalOpen: false })
        }
      />
      <AlertModal
        isOpen={state.isFailureEditChannelModalOpen}
        title="Save Changes Alert"
        variant="error"
        icon="times-circle"
        header="OH, SNAP!"
        subHeader={
          ERROR_MESSAGES.subHeader[state.editChannelError] ||
          ERROR_MESSAGES.subHeader.default
        }
        description={
          ERROR_MESSAGES.description[state.editChannelError] ||
          ERROR_MESSAGES.description.default
        }
        confirmText="Go back"
        handleConfirm={() => {
          setState({
            ...state,
            isFailureEditChannelModalOpen: false,
            editChannelError: null,
          });
        }}
        handleClose={() =>
          setState({
            ...state,
            isFailureEditChannelModalOpen: false,
            editChannelError: null,
          })
        }
      />
      <AlertModal
        isOpen={state.isLeavingWhileEditing}
        title="New Account Status"
        icon="question-circle"
        variant="warn"
        header="THERE ARE UNSAVED CHANGES."
        subHeader="Are you sure you want to leave without saving New Channel."
        description="Your entry will be lost if you don't save them."
        handleClose={() => setState({ ...state, isLeavingWhileEditing: false })}
        cancelText="Discard Changes"
        confirmText="Go Back"
        handleCancel={() => {
          if (state.nextLocation) {
            history.push(state.nextLocation);
          }
        }}
        handleConfirm={() => {
          setState({ ...state, isLeavingWhileEditing: false });
        }}
      />
    </>
  );
};

ChannelInformation.propTypes = {
  match: PropTypes.object,
  history: PropTypes.object,
  location: PropTypes.object,
};

export default ChannelInformation;
