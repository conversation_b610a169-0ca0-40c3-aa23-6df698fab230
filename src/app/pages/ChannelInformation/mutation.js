import { gql } from '@apollo/client';

export const EDIT_CHANNEL = gql`
  mutation editChannel($data: UpdateChannel!, $where: ChannelFilter!) {
    updateChannel(data: $data, where: $where) {
      id
      name
      channelCode
      email
      ipAddress
      clientId
      clientSecret
      callbackUrl
      apiKey
      merchantKey
      merchantCode
      billType
      subMerchants {
        merchant
        serviceType
      }
      isCallbackEncrypted
      callbackPassPhrase
      enableCardHolderName
      cardPaymentMethod
      gcashPaymentMethod
      bankPaymentMethod
      cardHolderNameType
      paymentGateway
      gcreditSubMerchantId
      isForPayByLink
      isSecureConnection
      gateways {
        xendit {
          card_straight
          card_installment
          otc
        }
      }
    }
  }
`;
