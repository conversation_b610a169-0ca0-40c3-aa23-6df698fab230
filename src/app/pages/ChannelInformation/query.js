import { gql } from '@apollo/client';

export const GET_CHANNEL_INFORMATION = gql`
  query getChannelInformation($where: ChannelFilter!) {
    channel(filter: $where) {
      id
      name
      channelCode
      email
      ipAddress
      isVerified
      clientId
      clientSecret
      callbackUrl
      emailNotif
      enablePaymentSession
      merchantCode
      merchantKey
      apiKey
      globeEmailNotification
      globeEmailNotificationPatternId
      innoveEmailNotification
      innoveEmailNotificationPatternId
      bayanEmailNotification
      bayanEmailNotificationPatternId
      billType
      subMerchants {
        merchant
        serviceType
      }
      failedEmailNotification
      enableCardHolderName
      isCallbackEncrypted
      callbackPassPhrase
      cardPaymentMethod
      gcashPaymentMethod
      ewalletPaymentMethod
      cardHolderNameType
      bankPaymentMethod
      isOrEnabled
      gcreditSubMerchantId
      isForPayByLink
      gcashOneClickEnabled
      gcashOneClickMerchantId
      gcashOneClickClientId
      gcashOneClickProductCode
      gcashOneClickRedirectUrl
      gcashOneClickBindingIdPrefix
      gcashOneClickValidity
      bindCallbackUrl
      isSecureConnection
      gateways {
        xendit {
          card_straight
          card_installment
          otc
        }
      }
    }
  }
`;

export const GET_GCASH_ONECLICK_DATA = gql`
  query getGCashOneClickData($where: ChannelFilter!) {
    channel(filter: $where) {
      gcashOneClickEnabled
      gcashOneClickMerchantId
      gcashOneClickClientId
      gcashOneClickProductCode
      gcashOneClickRedirectUrl
      gcashOneClickBindingIdPrefix
      gcashOneClickValidity
      gcashOneClickClientSecret
    }
  }
`;

export const GET_TRANSACTION_COUNT = gql`
  query getTransactionCount($filter: ChannelTransactionCountFilter!) {
    channelTransactionCount(filter: $filter) {
      count
    }
  }
`;
