import { useQuery } from '@apollo/client';

// The hook is now much simpler and more declarative!
function useQueryReportSeries(query, path, values) {
  const { filter, pagination } = values;

  const { data, error, loading, fetchMore } = useQuery(query, {
    variables: {
      filter,
      pagination: {
        limit: pagination.limit,
        startKey: pagination.startKey || null, // Initial startKey
      },
    },
    // Use cache-first to show cached data instantly while refetching in the background
    fetchPolicy: 'cache-and-network',
  });

  const queryResult = data?.[path];
  const flattenedData = queryResult?.filteredData || [];

  const loadMore = () => {
    // Don't try to fetch more if we're already loading or there's no more data
    if (loading || !queryResult?.lastKey) {
      return;
    }

    fetchMore({
      variables: {
        pagination: {
          limit: pagination.limit,
          // Use the lastKey from the previous fetch to get the next page
          startKey: queryResult.lastKey,
        },
      },
    });
  };

  // The 'isLastPage' logic is now trivial
  const isLastPage = !loading && !!queryResult && !queryResult.lastKey;

  return {
    // The hook now returns clean, reliable data and state from Apollo
    data: flattenedData, // The full, merged list of items
    loading,
    error,
    loadMore, // Expose a function to load the next page
    isLastPage,
  };
}

export default useQueryReportSeries;
