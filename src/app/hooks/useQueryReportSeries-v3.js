import { useState, useEffect } from 'react';
import { useQuery } from '@apollo/client';

function useQueryReportSeries(query, path, values) {
  const [filter, setFilter] = useState({});
  const [currentPage, setCurrentPage] = useState(1);
  const [limit, setLimit] = useState(values.pagination?.limit || 10);

  // Merge any default filters from values
  let queryFilter = { ...filter };
  if (values && values.filter) {
    queryFilter = { ...queryFilter, ...values.filter };
  }

  const {
    data: queryData,
    loading,
    error,
    fetchMore,
    refetch,
  } = useQuery(query, {
    variables: {
      pagination: { limit },
      filter: queryFilter,
    },
    fetchPolicy: 'cache-and-network',
    notifyOnNetworkStatusChange: true,
  });

  const queryResult = queryData?.[path];
  const allData = queryResult?.filteredData || [];

  // Transform data to flatten settlementBreakdown if needed
  const transformedData =
    (path === 'reports' ||
      path === 'billingReports' ||
      path === 'installmentReport' ||
      path === 'transactionLogs') &&
    allData.length > 0 &&
    allData[0]?.settlementBreakdown
      ? allData.flatMap(transaction => {
          if (
            transaction.settlementBreakdown &&
            transaction.settlementBreakdown.length > 0
          ) {
            return transaction.settlementBreakdown.map(settlement => ({
              ...transaction,
              settlement,
            }));
          }
          return transaction;
        })
      : allData;

  // Calculate pagination for current page view
  const startIndex = (currentPage - 1) * limit;
  const endIndex = startIndex + limit;
  const currentPageData = transformedData.slice(startIndex, endIndex);

  // Auto-fetch more data when needed
  useEffect(() => {
    const needsMoreData =
      transformedData.length < endIndex && queryResult?.lastKey && !loading;

    if (needsMoreData) {
      fetchMore({
        variables: {
          pagination: {
            limit,
            startKey: queryResult.lastKey,
          },
        },
      }).catch(console.error);
    }
  }, [
    currentPage,
    limit,
    transformedData.length,
    queryResult?.lastKey,
    loading,
  ]);

  const isLastPage =
    !queryResult?.lastKey && transformedData.length <= endIndex;

  // Legacy-compatible pagination object for DataTable
  const pagination = {
    startKey: '',
    limit,
  };

  const setNewPagination = newPagination => {
    if (newPagination.limit !== limit) {
      setLimit(newPagination.limit);
      setCurrentPage(1); // Reset to first page when limit changes
    }
    // If startKey is reset (like in search), reset to first page
    if (newPagination.startKey === '') {
      setCurrentPage(1);
    }
  };

  const handleFilterChange = newFilter => {
    setFilter(newFilter);
    setCurrentPage(1); // Reset to first page when filter changes
  };

  return {
    // Data
    data: currentPageData,
    loading,
    error,

    // Legacy-compatible pagination interface
    pagination,
    setNewPagination,
    page: currentPage,
    setPage: setCurrentPage,
    isLastPage,

    // Filter management
    filter,
    setFilter: handleFilterChange,

    // Utility functions
    refetch,
    clearCache: () => {
      setCurrentPage(1);
      refetch();
    },
    loadData: () => refetch(),
    loadDataOnError: () => refetch(),
  };
}

export default useQueryReportSeries;
