import { useEffect, useState } from 'react';
import { useQuery } from '@apollo/client';

function sortObject(unordered) {
  const ordered = {};
  Object.keys(unordered)
    .sort()
    .forEach(function (key) {
      ordered[key] = unordered[key];
    });
  return ordered;
}

function useQueryReportSeries(query, path, values) {
  const [filter, setFilter] = useState({});
  const [pagination, setNewPagination] = useState(values.pagination);
  const [cache, setCache] = useState({});
  const [tempCache, setTempCache] = useState({});
  const [page, setPage] = useState(1);

  let queryFilter = { ...filter };
  if (values && values.filter) {
    queryFilter = { ...queryFilter, ...values.filter };
  }

  const {
    data: queryData,
    error,
    refetch,
  } = useQuery(query, {
    variables: {
      pagination: {
        limit: pagination.limit,
        startKey: pagination.startKey,
      },
      filter: queryFilter,
    },
    fetchPolicy: 'cache-and-network',
  });

  const key = JSON.stringify({
    filter: sortObject(filter),
    limit: pagination.limit,
  });

  function loadData() {
    setCache({});
  }

  function loadDataOnError() {
    setTempCache({ cache });
    setCache({ tempCache });
  }

  useEffect(() => {
    if (queryData && queryData[path]) {
      const newData = queryData[path].filteredData || [];

      if (!cache[key]) {
        // Initial cache setup
        setCache({
          ...cache,
          [key]: newData,
        });
      } else {
        // Check if we need to append new data
        const existingData = cache[key];
        const isNewDataDifferent =
          JSON.stringify(newData) !==
          JSON.stringify(existingData.slice(-newData.length));

        if (isNewDataDifferent && newData.length > 0) {
          setCache({
            ...cache,
            [key]: [...existingData, ...newData],
          });
        }
      }
    }
  }, [JSON.stringify(queryData)]);

  useEffect(() => {
    setNewPagination({
      ...pagination,
      startKey: values.pagination.startKey,
    });
    setPage(1);
  }, [filter, pagination.limit]);

  // Handle page changes - fetch more data if needed
  useEffect(() => {
    if (queryData && queryData[path] && cache[key] && page > 1) {
      const currentDataLength = cache[key].length;
      const requiredDataLength = pagination.limit * page;

      // If we need more data and there's a lastKey, fetch more
      if (currentDataLength < requiredDataLength && queryData[path].lastKey) {
        setNewPagination({
          ...pagination,
          startKey: queryData[path].lastKey,
        });
      }
    }
  }, [page]);

  const cachedData =
    (cache[key] &&
      cache[key].slice(
        (page - 1) * pagination.limit,
        page * pagination.limit
      )) ||
    [];

  // Transform data to flatten settlementBreakdown if needed
  const data =
    (path === 'reports' ||
      path === 'billingReports' ||
      path === 'installmentReport' ||
      path === 'transactionLogs') &&
    cachedData.length > 0 &&
    cachedData[0].settlementBreakdown
      ? cachedData.flatMap(transaction => {
          if (
            transaction.settlementBreakdown &&
            transaction.settlementBreakdown.length > 0
          ) {
            return transaction.settlementBreakdown.map(settlement => ({
              ...transaction,
              settlement,
            }));
          }
          return transaction;
        })
      : cachedData;

  const loading =
    !(queryData && queryData[path]) ||
    (queryData[path] &&
      queryData[path].lastKey !== null &&
      cache[key] &&
      cache[key].length < pagination.limit * page);

  function clearCache() {
    setCache({});
    if (page !== 1) setPage(1);
    if (JSON.stringify(values.pagination) !== JSON.stringify(pagination))
      setNewPagination(values.pagination);
    refetch();
  }

  return {
    refetch,
    data,
    setFilter,
    setNewPagination,
    pagination,
    filter,
    loading,
    page,
    setPage,
    error,
    isLastPage: (() => {
      const hasQueryData = !!(queryData && queryData[path]);
      const hasLastKey = !!(
        queryData &&
        queryData[path] &&
        queryData[path].lastKey
      );

      // If we don't have query data yet, we can't determine if it's the last page
      if (!hasQueryData) {
        return false;
      }

      const cachedDataLength = cache[key]?.length || 0;

      // Calculate the start and end indices for the current page
      const startIndex = (page - 1) * pagination.limit;
      const endIndex = page * pagination.limit;

      // Get the actual data for the current page
      const currentPageData = cache[key]?.slice(startIndex, endIndex) || [];

      // We're on the last page if:
      // 1. There's no lastKey from the API (no more data to fetch), AND
      // 2. Either:
      //    a. The current page has less data than the limit, OR
      //    b. We've reached the end of our cached data and there's no more to fetch
      const isLast =
        !hasLastKey &&
        (currentPageData.length < pagination.limit ||
          (cachedDataLength <= endIndex && !hasLastKey));

      return isLast;
    })(),
    clearCache,
    loadData,
    loadDataOnError,
  };
}

export default useQueryReportSeries;
